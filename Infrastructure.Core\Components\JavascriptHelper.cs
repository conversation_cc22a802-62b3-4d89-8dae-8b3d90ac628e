﻿using Microsoft.JSInterop;

namespace LendQube.Infrastructure.Core.Components;

public static class JavascriptHelper
{
    public static ValueTask<string> GetBrowserTimezone(this IJSRuntime jSRuntime, CancellationToken ct) => jSRuntime.InvokeAsync<string>("blazorExtensions.GetBrowserTimeZone", ct);
    public static ValueTask RunFeather(this IJSRuntime jSRuntime, CancellationToken ct) => jSRuntime.InvokeVoidAsync("blazorExtensions.RunFeather", ct);
    public static ValueTask CopyToClipboard(this IJSRuntime jsRuntime, string text, string elementId, CancellationToken ct) => jsRuntime.InvokeVoidAsync("blazorExtensions.CopyToClipboard", ct, text, elementId);
    public static ValueTask OpenModal(this IJSRuntime jSRuntime, string name, CancellationToken ct) => jSRuntime.InvokeVoidAsync("blazorExtensions.OpenModal", ct, name);
    public static ValueTask CloseModal(this IJSRuntime jSRuntime, string name, CancellationToken ct) => jSRuntime.InvokeVoidAsync("blazorExtensions.CloseModal", ct, name);
    public static ValueTask DownloadFile(this IJSRuntime jSRuntime, string name, string url, CancellationToken ct) => jSRuntime.InvokeVoidAsync("blazorExtensions.TriggerFileDownload", ct, name, url);
}
