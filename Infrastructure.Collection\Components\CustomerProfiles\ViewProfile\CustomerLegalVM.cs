﻿using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public sealed class CustomerLegalVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<CustomerLegal, CustomerLegalVM>> Mapping = data => new()
    {
        Id = data.Id,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId,
        TTPDate = data.TTPDate,
        TPPAmount = data.TPPAmount,
        SuspendedOrderGrantedDate = data.SuspendedOrderGrantedDate,
        SuspendedOrderAmount = data.SuspendedOrderAmount,
        SuspendedOrderActionDate = data.SuspendedOrderActionDate,
        Source = data.Source,
        SIDNumber = data.SIDNumber,
        ScotClaimSubmitDate = data.ScotClaimSubmitDate,
        NotSuitableReason = data.NotSuitableReason,
        CaseReference = data.CaseReference,
        ClaimAmount = data.ClaimAmount,
        ClaimIssueDate = data.ClaimIssueDate,
        Country = data.Country,
        Court = data.Court,
        DecreeGrantedDate = data.DecreeGrantedDate,
        EACourtFeePaidDate = data.EACourtFeePaidDate,
        EAServeDate = data.EAServeDate,
        LDORDate = data.LDORDate,
        MCOLCCJAppliedFor = data.MCOLCCJAppliedFor,
        MCOLCCJGranted = data.MCOLCCJGranted,
        MCOLFileDate = data.MCOLFileDate,
        NoaDate = data.NoaDate,
        NotSuitableForMCOL = data.NotSuitableForMCOL
    };

    public CustomerLegal Get(string profileId) => new()
    {
        Id = Id,
        ProfileId = profileId,
        CaseReference = CaseReference,
        ClaimAmount = ClaimAmount,
        ClaimIssueDate = ClaimIssueDate,
        Country = Country,
        Court = Court,
        DecreeGrantedDate = DecreeGrantedDate,
        EACourtFeePaidDate = EACourtFeePaidDate,
        LDORDate = LDORDate,
        EAServeDate = EAServeDate,
        MCOLCCJAppliedFor = MCOLCCJAppliedFor,
        MCOLCCJGranted = MCOLCCJGranted,
        MCOLFileDate = MCOLFileDate,
        NoaDate = NoaDate,
        NotSuitableForMCOL = NotSuitableForMCOL,
        NotSuitableReason = NotSuitableReason,
        ScotClaimSubmitDate = ScotClaimSubmitDate,
        SIDNumber = SIDNumber,
        Source = Source,
        SuspendedOrderActionDate = SuspendedOrderActionDate,
        SuspendedOrderAmount = SuspendedOrderAmount,
        SuspendedOrderGrantedDate = SuspendedOrderGrantedDate,
        TPPAmount = TPPAmount,
        TTPDate = TTPDate,
    };

    public string SIDNumber { get; set; }
    public string CaseReference { get; set; }
    public decimal ClaimAmount { get; set; }
    public string Court { get; set; }
    public string Country { get; set; }
    public DateOnly? ScotClaimSubmitDate { get; set; }
    public DateOnly? LDORDate { get; set; }
    public DateOnly? DecreeGrantedDate { get; set; }
    public DateOnly? MCOLFileDate { get; set; }
    public DateOnly? MCOLCCJAppliedFor { get; set; }
    public DateOnly? MCOLCCJGranted { get; set; }
    public DateOnly? TTPDate { get; set; }
    public decimal TPPAmount { get; set; }
    public DateOnly? EACourtFeePaidDate { get; set; }
    public DateOnly? EAServeDate { get; set; }
    public DateOnly? SuspendedOrderGrantedDate { get; set; }
    public DateOnly? SuspendedOrderActionDate { get; set; }
    public decimal SuspendedOrderAmount { get; set; }
    public DateOnly? ClaimIssueDate { get; set; }
    public DateOnly? NotSuitableForMCOL { get; set; }
    public string NotSuitableReason { get; set; }
    public DateOnly? NoaDate { get; set; }
    public string Source { get; set; }
}

