﻿using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Helpers;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.FileManagement;
using Microsoft.AspNetCore.Components.Forms;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;
public partial class ViewProfile
{
    private DataTable<CustomerDocument> customerDocumentsTable;
    private ColumnList customerDocumentsTableDefinition;
    private static readonly string TEMPLATES_UPLOADKEY = "CustomerDocuments";
    private IBrowserFile customerDocumentFile;
    private string AddCustomerDocumentModal => "AddCustomerDocumentModal";

    private CustomerDocument AddCustomerDocumentModel { get; set; } = new();

    private void SetupCustomerDocuments()
    {
        customerDocumentsTableDefinition = CrudService.GetTableDefinition<CustomerDocument>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerDocumentPermission)
        });

        customerDocumentsTableDefinition.TopActionButtons.Add(new TopActionButton("Add Document", ModalName: AddCustomerDocumentModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddCustomerDocumentPermission)));
        customerDocumentsTableDefinition.RowActionButtons.Add(new RowActionButton("Download", Icon: "download", Action: async (object row) =>
        {
            CloseMessage();
            customerDocumentsTable.Loading = true;
            var template = row as CustomerDocument;
            await JSRuntime.DownloadFile(Path.GetFileName(template.FileUrl), template.FileUrl, Cancel);
            customerDocumentsTable.Loading = false;

            StateHasChanged();
        }, ShowCondition: (object row) => !string.IsNullOrWhiteSpace((row as CustomerDocument).FileUrl)));
        customerDocumentsTable.SetTableDefinition(customerDocumentsTableDefinition);
    }

    private ValueTask<TypedBasePageList<CustomerDocument>> LoadCustomerDocuments(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerDocument>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, ct);
    }

    private void LoadCustomerDocumentFile(InputFileChangeEventArgs e)
    {
        customerDocumentFile = e.File;
    }

    private ValueTask SubmitNewCustomerDocument() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddCustomerDocumentPermission, AddCustomerDocumentModal, async () =>
    {
        if (customerDocumentFile == null)
        {
            ModalMessage.Error("Please select a file to upload");
            return false;
        }

        var fileName = FileManagementService.GenerateFileName($"{customerDocumentFile.Name}_{SecurityDriven.FastGuid.NewGuid()}", customerDocumentFile.Name);
        fileName = $"{Data.Id}/{fileName}";
        var uploadResult = await FileManagementService.SaveBrowserFile(TEMPLATES_UPLOADKEY, fileName, customerDocumentFile, MimeDetectorHelper.ImageTypeWithDocument, Cancel);
        if (!uploadResult.IsSuccessful)
        {
            ModalMessage.Error($"Could not upload a file.");
            return false;
        }
        AddCustomerDocumentModel.ProfileId = Data.Id;
        AddCustomerDocumentModel.FileUrl = uploadResult.Data;

        uow.Db.Insert(AddCustomerDocumentModel);
        await uow.SaveAsync(Cancel);

        return true;
    }, () =>
    {
        AddCustomerDocumentModel = new();
        StateHasChanged();
        return customerDocumentsTable.Refresh();
    });

    private ValueTask<bool> DeleteCustomerDocument(CustomerDocument data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerDocumentPermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerDocument>(x => x.Id == data.Id, ct);
        return result > 0;
    }, refresh);
       
}

