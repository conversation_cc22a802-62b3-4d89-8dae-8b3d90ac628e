﻿namespace LendQube.Entities.Core.Messaging;

[Flags]
public enum MessageChannel
{
    Sms = 1 << 0,
    Email = 1 << 2,
    WhatsApp = 1 << 3,
    PushNotification = 1 << 4,
    PushNotificationOrSms = 1 << 5,
    PushNotificationAndSms = 1 << 6,
    PushNotificationOrEmail = 1 << 7,
    PushNotificationAndEmail = 1 << 8,
    SmsAndEmail = 1 << 9,
    SmsOrEmail = 1 << 10,
    CustomerInbox = 1 << 11,
    Telegram = 1 << 12,
    EmailOrSms = 1 << 13,
    Text = Sms | PushNotification | WhatsApp | PushNotificationOrSms | PushNotificationAndSms | PushNotificationOrEmail | PushNotificationAndEmail | SmsAndEmail | SmsOrEmail | CustomerInbox | Telegram | EmailOrSms,
}


[Flags]
public enum MessageConfigTemplateType
{
    Text = 1 << 0,
    Html = 1 << 2,
}

public enum MessageStatus
{
    WaitingForConfiguration,
    Queued,
    Processing,
    Failed,
    SentPartially,
    Sent,
    Delivered,
    Opened
}


public enum MessageTemplateSystemTags //only for system swappable tags
{
    Body,
    Logo,
    Name,
    FirstName,
    LastName
}