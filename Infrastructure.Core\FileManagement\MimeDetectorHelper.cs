﻿using LendQube.Infrastructure.Core.Helpers.Utils;
using MimeDetective;
using MimeDetective.Definitions;
using MimeDetective.Storage;
using System.Collections.Immutable;

namespace LendQube.Infrastructure.Core.FileManagement;

public static class MimeDetectorHelper
{
    public readonly static string[] PdfType = ["pdf"];
    public readonly static string[] ImageTypes = ["jpg", "jpeg", "png"];
    public readonly static string[] ImageTypesWithPdf = [.. ImageTypes, .. PdfType];
    public readonly static string[] ExcelTypes = ["xls", "csv", "xlsx"];
    public readonly static string[] WordTypes = ["doc", "docx"];
    public readonly static string[] DocumentTypes = [.. ExcelTypes, .. PdfType, .. WordTypes];
    public readonly static string[] ImageTypeWithDocument = [.. ImageTypesWithPdf, .. DocumentTypes];

    public readonly static string[] ExcelMimeTypes = ["xls", "csv", "xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-excel"];
    public readonly static string[] DocumentMimeTypes = [.. ExcelMimeTypes, .. PdfType];

    private readonly static IContentInspector inspector;
    static MimeDetectorHelper()
    {
        string[] extensions = [.. ImageTypes, .. DocumentTypes];

        var scopedDefinitions = DefaultDefinitions.All()
            .ScopeExtensions(extensions)
            .TrimDescription()
            .TrimMimeType()
            .ToImmutableArray();

        inspector = new ContentInspectorBuilder()
        {
            Definitions = scopedDefinitions
        }.Build();
    }

    public static string GetFileType(this byte[] data)
    {
        var results = inspector.Inspect(data);
        var fileType = results.ByFileExtension().FirstOrDefault(x => x.Matches.Any(y => y.Type == MimeDetective.Engine.DefinitionMatchType.Complete))?.Extension;
        return fileType;
    }

    public static (bool matches, string extension) ValidateFileType(this byte[] data, string[] extensions)
    {
        var results = inspector.Inspect(data);
        var fileType = results.ByFileExtension().FirstOrDefault(x => extensions.Contains(x.Extension));
        var matches = fileType?.Matches.Any(y => y.Type == MimeDetective.Engine.DefinitionMatchType.Complete) ?? false;
        return (matches, fileType?.Extension);
    }

    public static (bool matches, string extension) ValidateFileTypeAndCreationDate(this byte[] data, TimeSpan validityPeriod, string[] extensions)
    {
        var now = DateTime.UtcNow;
        var results = inspector.Inspect(data);
        var fileType = results.ByFileExtension().FirstOrDefault(x => extensions.Contains(x.Extension));
        var matches = fileType?.Matches.Any(y => y.Type == MimeDetective.Engine.DefinitionMatchType.Complete && y.Definition.Meta?.Created?.At != null && (now - y.Definition.Meta.Created.At.Value) <= validityPeriod) ?? false;
        return (matches, fileType?.Extension);
    }

    public static (bool matches, string extension) ValidateFileType(this Stream data, string[] extensions)
    {
        data.Position = 0;
        var results = inspector.Inspect(data);
        var fileType = results.ByFileExtension().FirstOrDefault(x => extensions.Contains(x.Extension));
        var matches = fileType?.Matches.Any(y => y.Type == MimeDetective.Engine.DefinitionMatchType.Complete) ?? false;
        data.Position = 0;
        return (matches, fileType?.Extension);
    }

    public static string GetExtensionsInWebAcceptFormat(this string[] data)
    {
        var extensions = data
            .Distinct()
            .Select(ext => ext.StartsWith(".") ? ext.ToLower() : $".{ext.ToLower()}")
            .ToList();

        var mimeTypes = extensions
            .Select(ext => ContentTypeHelper.GetContentType(ext))
            .Where(mime => !string.IsNullOrWhiteSpace(mime));

        return string.Join(", ", extensions.Concat(mimeTypes).Distinct());
    }
}
