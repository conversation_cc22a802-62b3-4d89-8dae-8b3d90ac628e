﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using NodaTime;

namespace LendQube.Entities.Collection.Customers;

public class CustomerFee : BaseEntityWithIdentityId<CustomerFee>
{
    [DbGuid]
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public decimal TraceEndGTC { get; set; }
    public DateOnly? TraceEndGTCDate { get; set; }
    public decimal TraceInform { get; set; }
    public DateOnly? TraceInformDate { get; set; }
    public decimal CourtFee { get; set; }
    public DateOnly? CourtFeeDate { get; set; }
    public decimal CostOfRaisingClaim { get; set; }
    public DateOnly? CostOfRaisingClaimDate { get; set; }
    public decimal CAPSFee { get; set; }
    public DateOnly? CAPSFeeDate { get;set; }
}
