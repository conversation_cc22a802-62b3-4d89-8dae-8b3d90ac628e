﻿using System.Linq.Expressions;
using System.Text.Json.Serialization;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Database.DataPager;

public abstract class IAbstractPagedList
{
    [JsonIgnore]
    internal bool AsyncNotSupportedDataSource { get; init; }
    [JsonIgnore]
    internal bool IsCacheable { get; set; }
    public long Total { get; set; }
    [JsonIgnore]
    internal virtual int SkipBy { get; init; }
    [JsonIgnore]
    public long ResultSetTotal { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public virtual long From { get; init; }
    public virtual long To { get; init; }
    public virtual int LastPage { get; init; }
    public virtual bool HasPreviousPage { get; init; }
    public virtual bool HasNextPage { get; init; }
    public virtual long NextPageNumber { get; init; }
    public virtual long PreviousPageNumber { get; init; }
    [JsonIgnore]
    public string OrderByColumn { get; set; }
    [JsonIgnore]
    public DataOrderDirection OrderByDirection { get; set; }
}

public abstract class AbstractPagedList<T> : IAbstractPagedList
{
    protected AbstractPagedList(DataFilterAndPage filterAndPage)
    {
        CurrentPage = filterAndPage.Page > 0 ? filterAndPage.Page : 1;
        PageSize = filterAndPage.PageSize > 0 ? (filterAndPage.PageSize > 100 ? 10 : filterAndPage.PageSize) : 10;
        OrderByDirection = filterAndPage.OrderByDirection;
        OrderByColumn = filterAndPage.OrderByColumn;
        AsyncNotSupportedDataSource = filterAndPage.AsyncNotSupportedDataSource;
        IsCacheable = filterAndPage.IsCacheable;
    }

    protected AbstractPagedList(IQueryable<T> source, DataFilterAndPage filterAndPage) : this(filterAndPage)
    {
        Source = source;
        if (!string.IsNullOrEmpty(filterAndPage.OrderByColumn))
        {
            if (filterAndPage.OrderByDirection == DataOrderDirection.DESC)
                Source = Source.OrderByDescending(filterAndPage.OrderByColumn);
            else
                Source = Source.OrderBy(filterAndPage.OrderByColumn);
        }
    }

    protected async ValueTask Init(CancellationToken ct)
    {
        if (ct.IsCancellationRequested)
        {
            UnboxedData = [];
            return;
        }

        await SetCount(ct);

        CurrentPage = HasNextPage ? CurrentPage : LastPage;

        if (ct.IsCancellationRequested)
        {
            UnboxedData = [];
            return;
        }

        await UnpackData(ct);
    }

    protected virtual async Task UnpackData(CancellationToken ct)
    {
        var localSource = Source
                       .Skip(SkipBy)
                       .Take(PageSize);

        if (ct.IsCancellationRequested)
        {
            UnboxedData = [];
            return;
        }

        if (AsyncNotSupportedDataSource)
        {
            UnboxedData = [.. localSource];
        }
        else
        {
            Data = localSource.QCache(IsCacheable).ToListAsync(ct);
        }

        if (ct.IsCancellationRequested)
        {
            UnboxedData ??= [];
            return;
        }

        UnboxedData ??= Data != null ? await Data ?? [] : [];
    }

    protected virtual async ValueTask SetCount(CancellationToken ct)
    {
        Total = AsyncNotSupportedDataSource ? Source.Count() : await Source.CountAsync(ct);
        ResultSetTotal = ResultSetTotal > 0 ? ResultSetTotal : Total;
    }


    internal override int SkipBy => PageSize * (CurrentPage - 1);
    public override long From => (PageSize * (CurrentPage - 1)) + 1 > Total ? Total : (PageSize * (CurrentPage - 1)) + 1;
    public override long To => (PageSize * (CurrentPage - 1)) + PageSize > Total ? Total : (PageSize * (CurrentPage - 1)) + PageSize;
    public override int LastPage => Math.Max(1, (int)Math.Ceiling(Total / (double)PageSize));
    public override bool HasPreviousPage => CurrentPage > 1;
    public override bool HasNextPage => CurrentPage < LastPage;
    public override long NextPageNumber => HasNextPage ? CurrentPage + 1 : Total;
    public override long PreviousPageNumber => HasPreviousPage ? CurrentPage - 1 : 1;

    internal List<T> UnboxedData { get; set; }
    [JsonIgnore]
    internal Task<List<T>> Data { get; set; }
    protected IQueryable<T> Source { get; init; }
    public async ValueTask<TypedBasePageList<T>> ToType(CancellationToken ct)
    {
        await Init(ct);
        return new(this);
    }

}

public abstract class AbstractPagedList<T, TVM> : AbstractPagedList<T>
{
    protected AbstractPagedList(IQueryable<T> source, DataFilterAndPage filterAndPage, ISpecification<T> spec, Expression<Func<T, TVM>> selector) : base(source, filterAndPage)
    {
        LocalSource = Source.Select(selector);

        var whereClause = spec.GetAllFilter<TVM>(filterAndPage);
        LocalSource = LocalSource.QWhere(whereClause);

        if (!string.IsNullOrEmpty(filterAndPage.OrderByColumnVM))
        {
            if (filterAndPage.OrderByDirection == DataOrderDirection.DESC)
                LocalSource = LocalSource.OrderByDescending(filterAndPage.OrderByColumnVM);
            else
                LocalSource = LocalSource.OrderBy(filterAndPage.OrderByColumnVM);
            OrderByColumn = filterAndPage.OrderByColumnVM;
        }
    }

    protected AbstractPagedList(IQueryable<T> source, DataFilterAndPage filterAndPage, ISpecification<TVM> spec, Expression<Func<T, TVM>> selector) : base(source, filterAndPage)
    {
        LocalSource = Source.Select(selector).QWhere(spec.PrimaryCriteria);

        var whereClause = spec.GetAllFilter<TVM>(filterAndPage);
        LocalSource = LocalSource.QWhere(whereClause);

        if (!string.IsNullOrEmpty(filterAndPage.OrderByColumnVM))
        {
            if (filterAndPage.OrderByDirection == DataOrderDirection.DESC)
                LocalSource = LocalSource.OrderByDescending(filterAndPage.OrderByColumnVM);
            else
                LocalSource = LocalSource.OrderBy(filterAndPage.OrderByColumnVM);
            OrderByColumn = filterAndPage.OrderByColumnVM;
        }
    }

    protected IQueryable<TVM> LocalSource { get; init; }
    internal new List<TVM> UnboxedData { get; set; }
    [JsonIgnore]
    internal new Task<List<TVM>> Data { get; set; }

    protected override async Task UnpackData(CancellationToken ct)
    {
        var localSource = LocalSource
                       .Skip(SkipBy)
                       .Take(PageSize);

        if (ct.IsCancellationRequested)
        {
            UnboxedData = [];
            return;
        }

        if (AsyncNotSupportedDataSource)
        {
            UnboxedData = [.. localSource];
        }
        else
        {
            Data = localSource.QCache(IsCacheable).ToListAsync(ct);
        }

        if (ct.IsCancellationRequested)
        {
            UnboxedData ??= [];
            return;
        }

        UnboxedData ??= await Data ?? [];
    }

    protected override async ValueTask SetCount(CancellationToken ct)
    {
        Total = AsyncNotSupportedDataSource ? LocalSource.Count() : await LocalSource.CountAsync(ct);
        ResultSetTotal = ResultSetTotal > 0 ? ResultSetTotal : Total;
    }

    public new async ValueTask<TypedBasePageList<TVM>> ToType(CancellationToken ct)
    {
        await Init(ct);
        var filterAndPage = new TypedBasePageList<T, TVM>(this);
        return new TypedBasePageList<TVM>(this)
        {
            Data = filterAndPage.Data
        };
    }
}


