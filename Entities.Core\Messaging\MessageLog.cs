﻿using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Npgsql;
using Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure;

namespace LendQube.Entities.Core.Messaging;

public class MessageLog : BaseEntityWithHiloId, IEntityHasEnum, IEntityHasNotifyTrigger, IEntityTypeConfiguration<MessageLog>
{
    public string OriginatedFrom { get; set; }
    public MessageStatus Status { get; set; }
    public int AttemptCount { get; set; }
    public virtual ICollection<MessageLogEntry> MessageLogEntries { get; set; }
    public virtual ICollection<MessageLogActivity> Activity { get; set; }

    #region Change Trigger Notification
    public TriggerChange[] ChangesToObserve => [TriggerChange.Insert, TriggerChange.Update];
    public TriggerType[] Types => [TriggerType.After];
    public bool TrackOldData => false;
    public bool ReturnOnlyId => true;
    public string ConditionScript => $@"NEW.""{nameof(Status)}"" = '{MessageStatus.Queued}'";
    #endregion

    public string Schema => CoreEntityConfig.DefaultSchema;

    public void RegisterEnumInDataSource(NpgsqlDataSourceBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<MessageStatus>($"{Schema}.{nameof(MessageStatus)}", nameTranslator);
    }

    public void RegisterEnumInDataSource(NpgsqlDbContextOptionsBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<MessageStatus>(nameof(MessageStatus), Schema, nameTranslator);
    }

    public void Configure(EntityTypeBuilder<MessageLog> builder)
    {
        builder.HasIndex(e => e.Status);
    }
}

public class MessageLogEntry : BaseEntityWithIdentityId<MessageLogEntry>
{
    public long MessageLogId { get; set; }
    public virtual MessageLog Log { get; set; }
    public long? MessageConfigurationId { get; set; }
    public virtual MessageConfiguration? Config { get; set; }
    public string Name { get; set; }
    public string Subject { get; set; }
    public string SenderEmail { get; set; }
    public string SenderName { get; set; }
    public MessageChannel Channels { get; set; }
    public List<string> Keys { get; set; } = [];
    public bool TextRequired { get; set; }
    public bool EmailRequired { get; set; }
    public string TextTemplate { get; set; }
    public string HtmlTemplate { get; set; }
    public string BodyTextTemplate { get; set; }
    public string BodyHtmlTemplate { get; set; }
    public string ContainerTextTemplate { get; set; }
    public string ContainerHtmlTemplate { get; set; }
    public List<MessageRecipient> Recipients { get; set; } = [];
    public List<MessageCopiedIn> CopiedIn { get; set; }

    public override void Configure(EntityTypeBuilder<MessageLogEntry> builder)
    {
        base.Configure(builder);

        builder.OwnsMany(x => x.CopiedIn, x => x.ToJson());

        builder.OwnsMany(x => x.Recipients, x =>
        {
            x.ToJson();
            x.Property(x => x.UserId).HasConversion<Guid?>();
            x.OwnsOne(y => y.AdHoc, y =>
            {
                y.OwnsOne(z => z.PhoneNumber);
            });
            x.OwnsMany(x => x.TemplateValues);
            x.OwnsMany(x => x.Attachments);
            x.OwnsMany(x => x.CopiedIn);
        });

    }
}

public class MessageRecipient
{
    public string UserId { get; set; }
    public long? MessagingGroupId { get; set; }
    public AdHocRecipient? AdHoc { get; set; }
    public List<TemplateKeyValue> TemplateValues { get; set; }
    public List<MessageAttachment> Attachments { get; set; }
    public List<MessageCopiedIn> CopiedIn { get; set; }

    public bool IsValid() => !string.IsNullOrEmpty(UserId) || MessagingGroupId.HasValue || AdHoc != null;
}

public class AdHocRecipient
{
    public string Key { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public PhoneNumber? PhoneNumber { get; set; }
}

public record MessageAttachment(string FileName, string Url);
public record TemplateKeyValue(string Key, string Value);
public record MessageCopiedIn(string Name, string Email, string PhoneNumber, bool BlindCopy);