﻿using LendQube.Entities.Core.Location;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.DbContexts;
using LendQube.Infrastructure.Core.Database.Repository;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace LendQube.Infrastructure.Core.Messaging;

internal static class MessagingCompiledQueries
{
    private static readonly Func<AppDbContext, string, ProviderConfigVM> GetProvidersConfig =
        EF.CompileQuery((AppDbContext context, string name) => context.Set<MessageProviderConfig>().Where(x => x.Name == name)
        .Select(x => new ProviderConfigVM { ProviderId = x.Id, Disabled = x.DisabledOn.HasValue, LogActivity = x.LogActivityOn.HasValue })
        .AsNoTracking()
        .FirstOrDefault());

    public static ProviderConfigVM GetProviderConfig(IUnitofWork uow, string name) => GetProvidersConfig(uow.Context, name);


    private static readonly Func<AppDbContext, string, IEnumerable<ProviderConfigVM>> GetProviderSupportedCountriesAndConfig =
        EF.CompileQuery((AppDbContext context, string name) =>
        (from country in context.Set<Country>()
         from config in context.Set<MessageProviderConfig>()
         where config.Name == name && config.SupportedCountries.Contains(country.Code)
         select new ProviderConfigVM { ProviderId = config.Id, Disabled = config.DisabledOn.HasValue, LogActivity = config.LogActivityOn.HasValue, PhoneCode = country.PhoneCode }
        ).AsNoTracking());

    public static IReadOnlyList<ProviderConfigVM> GetMessageProviderSupportedCountriesAndConfig(IUnitofWork uow, string name) => GetProviderSupportedCountriesAndConfig(uow.Context, name).ToList().AsReadOnly();


    private static readonly Func<AppDbContext, List<long>, CancellationToken, Task<bool>> ConfirmMessageConfigsExist =
        EF.CompileAsyncQuery((AppDbContext context, List<long> ids, CancellationToken ct) => context.Set<MessageConfiguration>().Where(x => ids.Contains(x.Id)).Any());

    public static Task<bool> DoesMessageConfigsExist(IUnitofWork uow, List<long> ids, CancellationToken ct) => ConfirmMessageConfigsExist(uow.Context, ids, ct);


    private static readonly Func<AppDbContext, IEnumerable<string>, IAsyncEnumerable<MessageConfigIdAndName>> GetMessageConfigsByNames =
        EF.CompileAsyncQuery((AppDbContext context, IEnumerable<string> names) => context.Set<MessageConfiguration>().Where(x => names.Contains(x.Name.ToLower()))
        .Select(x => new MessageConfigIdAndName { Id = x.Id, Name = x.Name, NeedsConfiguration = x.NeedsConfiguration }));

    public static ValueTask<List<MessageConfigIdAndName>> GetMessageConfigIdsAndNamesByNames(IUnitofWork uow, ParallelQuery<string> names, CancellationToken ct) =>
        GetMessageConfigsByNames(uow.Context, names.Select(x => x.ToLower())).ToListAsync(ct);


    private static readonly Func<AppDbContext, IEnumerable<long?>, IEnumerable<string>, IEnumerable<long?>, IAsyncEnumerable<ConfigIdsAndUsersToSkip>> GetMessageExistsRecipientData =
        EF.CompileAsyncQuery((AppDbContext context, IEnumerable<long?> configIds, IEnumerable<string> userIds, IEnumerable<long?> messagingGroupIds) =>
        (from entry in context.Set<MessageLogEntry>()
         from item in entry.Recipients
         where configIds.Contains(entry.MessageConfigurationId) &&
         (entry.Log.Status >= MessageStatus.Sent || entry.Log.Status <= MessageStatus.Queued)
         && entry.Config.DoNotSendIfExists && ((SystemClock.Instance.GetCurrentInstant() - (entry.Log.LastModifiedDate ?? entry.Log.CreatedDate.Value)).TotalSeconds < entry.Config.ExistsCheckWindow)
         && (userIds.Contains(item.UserId) || messagingGroupIds.Contains(item.MessagingGroupId))
         select new ConfigIdsAndUsersToSkip
         {
             MessageLogId = entry.MessageLogId,
             ConfigId = entry.MessageConfigurationId.Value,
             UserId = item.UserId,
             MessagingGroupId = item.MessagingGroupId,
         }).AsNoTracking());

    public static ValueTask<List<ConfigIdsAndUsersToSkip>> GetMessageExistsForRecipientData(IUnitofWork uow, IEnumerable<long?> configIds, IEnumerable<string> userIds, IEnumerable<long?> messagingGroupIds, CancellationToken ct)
        => GetMessageExistsRecipientData(uow.Context, configIds, userIds, messagingGroupIds).ToListAsync(ct);
}


internal sealed class MessageConfigIdAndName
{
    public long Id { get; set; }
    public string Name { get; set; }
    public bool NeedsConfiguration { get; set; }
}

internal sealed class ProviderConfigVM
{
    public long ProviderId { get; set; }
    public bool Disabled { get; set; }
    public bool LogActivity { get; set; }
    public string PhoneCode { get; set; }
}