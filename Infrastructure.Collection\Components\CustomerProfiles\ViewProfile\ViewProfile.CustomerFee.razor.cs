﻿using EFCoreSecondLevelCacheInterceptor;
using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;
public partial class ViewProfile
{
    private DataTable<CustomerFeeVM> customerFeesTable;
    private ColumnList customerFeeTableDefinition;
    private string AddCustomerFeeModal => "AddCustomerFeeModal";        
     private string EditCustomerFeeModal => "EditCustomerFeeModal";

    private CustomerFee AddCustomerFeeModel { get; set; } = new();
    private CustomerFee EditCustomerFeeModel { get; set; } = new();

    private void SetupCustomerFee()
    {
        customerFeeTableDefinition = CrudService.GetTableDefinition<CustomerFee, CustomerFeeVM>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerFeePermission),
            HasEdit = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerFeePermission),
        });

        customerFeeTableDefinition.TopActionButtons.Add(new TopActionButton("Add", ModalName: AddCustomerFeeModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddCustomerFeePermission)));

        customerFeesTable.SetTableDefinition(customerFeeTableDefinition);

    }

    private ValueTask<TypedBasePageList<CustomerFeeVM>> LoadCustomerFee(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerFee>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, CustomerFeeVM.Mapping, ct: ct);
    }

    private ValueTask SubmitNewCustomerFee() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddCustomerFeePermission, AddCustomerFeeModal, async () =>
    {
        AddCustomerFeeModel.ProfileId = Data.Id;
        uow.Db.Insert(AddCustomerFeeModel);
        await uow.SaveAsync(Cancel);

        return true;
    }, () =>
    {
        AddCustomerFeeModel = new();
        StateHasChanged();
        return customerFeesTable.Refresh();
    });

    private ValueTask StartEditCustomerFee(CustomerFeeVM data, CancellationToken ct) => BaseEdit(ManageCustomersNavigation.CustomerProfileViewEditCustomerFeePermission, EditCustomerFeeModal, () =>
    {
        EditCustomerFeeModel = data.Get(Data.Id);
        return Task.CompletedTask;
    }, ct);

    private ValueTask SubmitEditCustomerFee() => BaseSaveEdit(null, EditCustomerFeeModal, async () =>
    {
        uow.Db.Update(EditCustomerFeeModel);
        await uow.SaveAsync(Cancel);
        return true;
    }, customerFeesTable.Refresh);


    private ValueTask<bool> DeleteCustomerFee(CustomerFeeVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerFeePermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerFee>(x => x.Id == data.Id, ct);
        return result > 0;
    }, refresh);

}
