﻿using System.Collections.Concurrent;
using LendQube.Infrastructure.Core.Telemetry;

namespace LendQube.Infrastructure.Core.Database.Repository.Concurrency;

public sealed class DbContextConcurrencyManager : IDisposable
{
    private readonly ConcurrentQueue<AbstractQueuedDbRequest> requestQueue;
    private readonly SemaphoreSlim semaphore;
    private readonly CancellationTokenSource cancellationTokenSource;
    private readonly Task processingTask;
    private readonly ILogManager<IRelationalDbRepository> logger;
    private volatile bool disposed;

    internal DbContextConcurrencyManager(ILogManager<IRelationalDbRepository> logger)
    {
        this.logger = logger;
        requestQueue = new ConcurrentQueue<AbstractQueuedDbRequest>();
        semaphore = new SemaphoreSlim(0);
        cancellationTokenSource = new CancellationTokenSource();

        // Start the processing pipeline
        processingTask = ProcessQueue(cancellationTokenSource.Token);
    }

    public async Task<T> Execute<T>(Func<Task<T>> operation, CancellationToken ct)
    {
        var tcs = new TaskCompletionSource<T>();
        var request = new QueuedDbRequest<T>(operation, tcs, ct);

        requestQueue.Enqueue(request);

        if (requestQueue.Count == 1)
            semaphore.Release();

        var result = await tcs.Task;

        requestQueue.TryDequeue(out _);

        if (!requestQueue.IsEmpty)
            semaphore.Release();

        return result;
    }

    public async ValueTask<T> Execute<T>(Func<ValueTask<T>> operation, CancellationToken ct)
    {
        var tcs = new TaskCompletionSource<T>();
        var request = new QueuedValueTaskDbRequest<T>(operation, tcs, ct);

        requestQueue.Enqueue(request);

        if (requestQueue.Count == 1)
            semaphore.Release();

        var result = await tcs.Task;
        requestQueue.TryDequeue(out _);

        if (!requestQueue.IsEmpty)
            semaphore.Release();

        return result;
    }

    public async ValueTask<T> Execute<T>(Func<ValueTask<T>> operation, CancellationTokenSource cts, CancellationToken ct)
    {
        var tcs = new TaskCompletionSource<T>();
        var request = new QueuedValueTaskDbRequest<T>(operation, tcs, cts, ct);

        requestQueue.Enqueue(request);

        if (requestQueue.Count == 1)
            semaphore.Release();
        else if (!requestQueue.IsEmpty && requestQueue.TryPeek(out var oldRequest) && oldRequest.CancellationTokenSource != null)
        {
            oldRequest.CancellationTokenSource.Cancel(false);
        }

        var result = await tcs.Task;
        requestQueue.TryDequeue(out _);

        if (!requestQueue.IsEmpty)
            semaphore.Release();

        return result;
    }

    public async Task Execute(Func<Task> operation, CancellationToken ct)
    {
        var tcs = new TaskCompletionSource<object>();
        var request = new QueuedDbRequest(operation, tcs, ct);

        requestQueue.Enqueue(request);

        if (requestQueue.Count == 1)
            semaphore.Release();

        await tcs.Task;
        requestQueue.TryDequeue(out _);

        if (!requestQueue.IsEmpty)
            semaphore.Release();
    }

    private async Task ProcessQueue(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // Wait for a request to be available
                await semaphore.WaitAsync(cancellationToken);

                if (requestQueue.TryPeek(out var request))
                {
                    await ProcessRequest(request);
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                logger.LogError(EventSource.Infrastructure, EventAction.ExceptionOrError, ex, "Concurrent processing failed");
            }
        }
    }

    private static async Task ProcessRequest(AbstractQueuedDbRequest request)
    {
        try
        {
            if (request.CancellationToken.IsCancellationRequested)
            {
                request.SetCanceled();
                return;
            }

            await request.Execute();
        }
        catch (Exception ex)
        {
            request.SetException(ex);
        }
    }

    public void Dispose()
    {
        GC.SuppressFinalize(this);

        if (disposed)
            return;

        disposed = true;

        cancellationTokenSource.Cancel();

        while (requestQueue.TryDequeue(out var request))
        {
            request.SetCanceled();
        }

        try
        {
            processingTask?.Wait(TimeSpan.FromSeconds(5));
        }
        catch (Exception)
        {
            // Ignore cleanup exceptions
        }

        semaphore?.Dispose();
        cancellationTokenSource?.Dispose();
    }
}
