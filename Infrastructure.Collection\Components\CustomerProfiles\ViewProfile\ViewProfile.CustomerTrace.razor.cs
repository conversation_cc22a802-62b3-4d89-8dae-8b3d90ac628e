﻿using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Specification;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;
public partial class ViewProfile
{
    private DataTable<CustomerTraceVM> customerTracesTable;
    private ColumnList customerTraceTableDefinition;
    private string AddCustomerTraceModal => "AddCustomerTraceModal";
    private string EditCustomerTraceModal => "EditCustomerTraceModal";
    private CustomerTrace AddCustomerTraceModel { get; set; } = new();
    private CustomerTrace EditCustomerTraceModel { get; set; } = new();

    private void SetupCustomerTrace()
    {
        customerTraceTableDefinition = CrudService.GetTableDefinition<CustomerTrace>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerTracePermission),
            HasEdit = HasClaim(ManageCustomersNavigation.CustomerProfileViewEditCustomerTracePermission),
        });

        customerTraceTableDefinition.TopActionButtons.Add(new TopActionButton("Add", ModalName: AddCustomerTraceModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddCustomerTracePermission)));

        customerTracesTable.SetTableDefinition(customerTraceTableDefinition);

    }

    private ValueTask<TypedBasePageList<CustomerTraceVM>> LoadCustomerTrace(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerTrace>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, CustomerTraceVM.Mapping, ct);
    }

    private ValueTask SubmitNewCustomerTrace() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddCustomerTracePermission, AddCustomerTraceModal, async () =>
    {
        AddCustomerTraceModel.ProfileId = Data.Id;
        uow.Db.Insert(AddCustomerTraceModel);
        await uow.SaveAsync(Cancel);

        return true;
    }, () =>
    {
        AddCustomerTraceModel = new();
        StateHasChanged();
        return customerTracesTable.Refresh();
    });

    private ValueTask StartEditCustomerTrace(CustomerTraceVM data, CancellationToken ct) => BaseEdit(ManageCustomersNavigation.CustomerProfileViewEditCustomerTracePermission, EditCustomerTraceModal, () =>
    {
        EditCustomerTraceModel = data.Get(Data.Id);
        return Task.CompletedTask;
    }, ct);

    private ValueTask SubmitEditCustomerTrace() => BaseSaveEdit(null, EditCustomerTraceModal, async () =>
    {
        uow.Db.Update(EditCustomerTraceModel);
        await uow.SaveAsync(Cancel);
        return true;
    }, customerTracesTable.Refresh);

    private ValueTask<bool> DeleteCustomerTrace(CustomerTraceVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerTracePermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerTrace>(x => x.Id == data.Id, ct);
        return result > 0;
    }, refresh);

}
