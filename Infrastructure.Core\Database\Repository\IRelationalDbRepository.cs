﻿using System.Linq.Expressions;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.Repository.Concurrency;
using LendQube.Infrastructure.Core.Database.Specification;
using Microsoft.EntityFrameworkCore.Query;

namespace LendQube.Infrastructure.Core.Database.Repository;

public interface IRelationalDbRepository
{
    DbContextConcurrencyManager ConcurrencyManager { get; }
    ValueTask<T> ByIdAsync<T>(object id, CancellationToken ct) where T : class, IBaseEntityForRelationalDb;
    void Delete<T>(T entity) where T : class, IBaseEntityForRelationalDb;
    Task<int> DeleteAndSaveWithFilterAsync<T>(Expression<Func<T, bool>> filter, CancellationToken ct) where T : class, IBaseEntityForRelationalDb;
    Task<bool> TruncateTable<T>(CancellationToken ct) where T : class, IBaseEntityForRelationalDb;
    void RollbackSingle<T>(T entity) where T : class, IBaseEntityForRelationalDb;
    void RollbackAll();

    Task<int> CountAsync<T>(Expression<Func<T, bool>> filter = null, CancellationToken ct = default) where T : class, IBaseEntityForRelationalDb;
    Task<bool> ExistsAsync<T>(Expression<Func<T, bool>> filter = null, CancellationToken ct = default) where T : class, IBaseEntityForRelationalDb;
    Task<bool> ExistsWithSpecAsync<T>(ISpecification<T> spec, CancellationToken ct) where T : class, IBaseEntityForRelationalDb;

    void Insert<T>(T entity) where T : class, IBaseEntityForRelationalDb;
    Task InsertBulkAsync<T>(IEnumerable<T> entities, CancellationToken ct) where T : BaseEntityWithHiloId;
    void InsertBulk<T>(IEnumerable<T> entities, CancellationToken ct) where T : class, IEntityDoesNotUseHiLoIdentity, IBaseEntityForRelationalDb;

    (bool, string) IsEntityValid<T>(T data) where T : class;

    List<T> Many<T>(SqlQueryBuilder<T> builder) where T : class, IBaseEntityForRelationalDb;
    Task<List<T>> ManyAsync<T>(SqlQueryBuilder<T> builder, CancellationToken ct) where T : class, IBaseEntityForRelationalDb;
    List<TVM> ManySelect<T, TVM>(SqlSelectQueryBuilder<T, TVM> builder)
        where T : class, IBaseEntityForRelationalDb;
    Task<List<TVM>> ManySelectAsync<T, TVM>(SqlSelectQueryBuilder<T, TVM> builder, CancellationToken ct)
        where T : class, IBaseEntityForRelationalDb;
    List<TVM> ManySelectWithSpec<T, TVM>(ISpecification<T> spec, Expression<Func<T, TVM>> selector, Expression<Func<TVM, bool>> filter = null, Func<IQueryable<TVM>, IOrderedQueryable<TVM>> orderBy = null)
        where T : class, IBaseEntityForRelationalDb;
    Task<List<TVM>> ManySelectWithSpecAsync<T, TVM>(ISpecification<T> spec, Expression<Func<T, TVM>> selector, Expression<Func<TVM, bool>> filter = null, Func<IQueryable<TVM>, IOrderedQueryable<TVM>> orderBy = null, CancellationToken ct = default)
        where T : class, IBaseEntityForRelationalDb;

    List<T> ManyWithSpec<T>(ISpecification<T> spec) where T : class, IBaseEntityForRelationalDb;
    Task<List<T>> ManyWithSpecAsync<T>(ISpecification<T> spec, CancellationToken ct) where T : class, IBaseEntityForRelationalDb;

    IQueryable<T> NotTrackedWithSpec<T>(ISpecification<T> spec) where T : class, IBaseEntityForRelationalDb;

    T One<T>(SqlQueryBuilder<T> builder) where T : class, IBaseEntityForRelationalDb;
    Task<T> OneAsync<T>(SqlQueryBuilder<T> builder, CancellationToken ct) where T : class, IBaseEntityForRelationalDb;
    TVM OneSelect<T, TVM>(SqlSelectQueryBuilder<T, TVM> builder)
        where T : class, IBaseEntityForRelationalDb;
    Task<TVM> OneSelectAsync<T, TVM>(SqlSelectQueryBuilder<T, TVM> builder, CancellationToken ct)
        where T : class, IBaseEntityForRelationalDb;
    TVM OneSelectWithSpec<T, TVM>(ISpecification<T> spec, Expression<Func<T, TVM>> selector, Expression<Func<TVM, bool>> filter = null, Func<IQueryable<TVM>, IOrderedQueryable<TVM>> orderBy = null)
        where T : class, IBaseEntityForRelationalDb;
    Task<TVM> OneSelectWithSpecAsync<T, TVM>(ISpecification<T> spec, Expression<Func<T, TVM>> selector, Expression<Func<TVM, bool>> filter = null, Func<IQueryable<TVM>, IOrderedQueryable<TVM>> orderBy = null, CancellationToken ct = default)
        where T : class, IBaseEntityForRelationalDb;
    T OneWithSpec<T>(ISpecification<T> spec) where T : class, IBaseEntityForRelationalDb;
    Task<T> OneWithSpecAsync<T>(ISpecification<T> spec, CancellationToken ct) where T : class, IBaseEntityForRelationalDb;

    IQueryable<T> Queryable<T>() where T : class, IBaseEntityForRelationalDb;

    void Update<T>(T entity) where T : class, IBaseEntityForRelationalDb;
    Task<int> UpdateAndSaveWithFilterAsync<T>(Expression<Func<T, bool>> filters, Expression<Func<SetPropertyCalls<T>, SetPropertyCalls<T>>> updates, CancellationToken ct) where T : class, IBaseEntityForRelationalDb;
    int UpdateAndSaveWithFilter<T>(Expression<Func<T, bool>> filters, Expression<Func<SetPropertyCalls<T>, SetPropertyCalls<T>>> updates) where T : class, IBaseEntityForRelationalDb;
    void UpdateBulk<T>(List<T> entities, CancellationToken ct) where T : class, IBaseEntityForRelationalDb;
    Task<bool> Upsert<T>(Dictionary<string, string> columnsWithValues, CancellationToken ct) where T : class, IBaseEntityForRelationalDb;

    void StampChangeTracker(CancellationToken ct);
}