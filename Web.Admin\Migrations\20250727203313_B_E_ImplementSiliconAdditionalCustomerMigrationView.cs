﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class B_E_ImplementSiliconAdditionalCustomerMigrationView : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CustomerEarningsArrestment",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    SiliconReference = table.Column<string>(type: "text", nullable: true),
                    CaseReference = table.Column<string>(type: "text", nullable: true),
                    AEReference = table.Column<string>(type: "text", nullable: true),
                    Court = table.Column<string>(type: "text", nullable: true),
                    LatestUpdate = table.Column<string>(type: "text", nullable: true),
                    LatestUpdateDate = table.Column<DateOnly>(type: "date", nullable: true),
                    LatestUpdateDescription = table.Column<string>(type: "text", nullable: true),
                    ActionRequired = table.Column<string>(type: "text", nullable: true),
                    ScannedDate = table.Column<DateOnly>(type: "date", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerEarningsArrestment", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerEarningsArrestment_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerEmployment",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    EmploymentStatus = table.Column<string>(type: "text", nullable: true),
                    EmploymentType = table.Column<string>(type: "text", nullable: true),
                    EmployerName = table.Column<string>(type: "text", nullable: true),
                    Occupation = table.Column<string>(type: "text", nullable: true),
                    MonthlySalaryAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    AnnualSalaryAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    EmployedStartDate = table.Column<DateOnly>(type: "date", nullable: true),
                    EmployedEndDate = table.Column<DateOnly>(type: "date", nullable: true),
                    SalaryFrequency = table.Column<string>(type: "text", nullable: true),
                    Note = table.Column<string>(type: "text", nullable: true),
                    Source = table.Column<string>(type: "text", nullable: true),
                    PayDate = table.Column<DateOnly>(type: "date", nullable: true),
                    PayDay = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerEmployment", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerEmployment_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerFee",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    TraceEndGTC = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TraceEndGTCDate = table.Column<DateOnly>(type: "date", nullable: true),
                    TraceInform = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TraceInformDate = table.Column<DateOnly>(type: "date", nullable: true),
                    CourtFee = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CourtFeeDate = table.Column<DateOnly>(type: "date", nullable: true),
                    CostOfRaisingClaim = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CostOfRaisingClaimDate = table.Column<DateOnly>(type: "date", nullable: true),
                    CAPSFee = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CAPSFeeDate = table.Column<DateOnly>(type: "date", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerFee", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerFee_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerLegal",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    SIDNumber = table.Column<string>(type: "text", nullable: true),
                    CaseReference = table.Column<string>(type: "text", nullable: true),
                    ClaimAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Court = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    ScotClaimSubmitDate = table.Column<DateOnly>(type: "date", nullable: true),
                    LDORDate = table.Column<DateOnly>(type: "date", nullable: true),
                    DecreeGrantedDate = table.Column<DateOnly>(type: "date", nullable: true),
                    MCOLFileDate = table.Column<DateOnly>(type: "date", nullable: true),
                    MCOLCCJAppliedFor = table.Column<DateOnly>(type: "date", nullable: true),
                    MCOLCCJGranted = table.Column<DateOnly>(type: "date", nullable: true),
                    TTPDate = table.Column<DateOnly>(type: "date", nullable: true),
                    TPPAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    EACourtFeePaidDate = table.Column<DateOnly>(type: "date", nullable: true),
                    EAServeDate = table.Column<DateOnly>(type: "date", nullable: true),
                    SuspendedOrderGrantedDate = table.Column<DateOnly>(type: "date", nullable: true),
                    SuspendedOrderActionDate = table.Column<DateOnly>(type: "date", nullable: true),
                    SuspendedOrderAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    ClaimIssueDate = table.Column<DateOnly>(type: "date", nullable: true),
                    NotSuitableForMCOL = table.Column<DateOnly>(type: "date", nullable: true),
                    NotSuitableReason = table.Column<string>(type: "text", nullable: true),
                    NoaDate = table.Column<DateOnly>(type: "date", nullable: true),
                    Source = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerLegal", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerLegal_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerTrace",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    RetrieverTraceDone = table.Column<string>(type: "text", nullable: true),
                    RetrieverTraceDate = table.Column<DateOnly>(type: "date", nullable: true),
                    RetrieverTraceResult = table.Column<string>(type: "text", nullable: true),
                    CCJVolume = table.Column<string>(type: "text", nullable: true),
                    CCJValue = table.Column<string>(type: "text", nullable: true),
                    HomeOwner = table.Column<string>(type: "text", nullable: true),
                    ProceedWithLegal = table.Column<string>(type: "text", nullable: true),
                    DODTraceDone = table.Column<string>(type: "text", nullable: true),
                    DODTraceDate = table.Column<DateOnly>(type: "date", nullable: true),
                    DODTraceResult = table.Column<string>(type: "text", nullable: true),
                    EndeavourTraceDone = table.Column<string>(type: "text", nullable: true),
                    EndeavourTraceDate = table.Column<DateOnly>(type: "date", nullable: true),
                    EndeavourTraceResult = table.Column<string>(type: "text", nullable: true),
                    OtherTraceDone = table.Column<string>(type: "text", nullable: true),
                    OtherTraceCompanyName = table.Column<string>(type: "text", nullable: true),
                    OtherTraceCompanyDate = table.Column<DateOnly>(type: "date", nullable: true),
                    OtherTraceResult = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerTrace", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerTrace_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_CustomerEarningsArrestment_ProfileId",
                schema: "collection",
                table: "CustomerEarningsArrestment",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerEmployment_ProfileId",
                schema: "collection",
                table: "CustomerEmployment",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerFee_ProfileId",
                schema: "collection",
                table: "CustomerFee",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerLegal_ProfileId",
                schema: "collection",
                table: "CustomerLegal",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerTrace_ProfileId",
                schema: "collection",
                table: "CustomerTrace",
                column: "ProfileId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CustomerEarningsArrestment",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerEmployment",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerFee",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerLegal",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerTrace",
                schema: "collection");
        }
    }
}
