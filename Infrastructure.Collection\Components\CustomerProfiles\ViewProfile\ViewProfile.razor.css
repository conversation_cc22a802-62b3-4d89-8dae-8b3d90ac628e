.info-col {
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
    border-radius: var(--card-radius);
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease; 
}

.info-col:hover {
    box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
}

.info-col .card {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: var(--card-radius);
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease;
}

.info-col .card:hover {
    transform: translateY(-2px);
}

.info-col .__header {
    background: linear-gradient(135deg, var(--core-primary) 0%, var(--core-dark-accent) 100%);
    color: var(--content-inverse);
    padding: 1.5rem;
    border-radius: var(--card-radius) var(--card-radius) 0 0;
}

.info-col .__title {
    font-size: 1.5rem; 
    font-weight: 600;
    display: block;
    margin-bottom: 0.5rem;
}
 
.info-col .__amount {
    font-size: 1rem;
    opacity: 0.9;
    display: block;
}

.info-col .status {
    margin-top: 1rem;
}

.info-col .status .lz {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

.info-col .status .lz.__yellow {
    background-color: var(--warning-fill);
    color: var(--content-inverse);
}

.info-col .status .lz.__red {
    background-color: var(--critical-fill);
    color: var(--content-inverse);
}

.info-col .detail-wrapper {
    padding: 1rem;
}

.info-col .detail-item {
    padding: 0.75rem;
    border-radius: var(--card-radius);
    background: var(--neutral-50);
    transition: all 0.2s ease;
    cursor: default;
    position: relative;
    overflow: hidden;
}

.info-col .detail-item:hover {
    background: var(--neutral-100);
    transform: scale(1.02);
}

.info-col .detail-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--core-primary);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.info-col .detail-item:hover::before {
    transform: translateX(0);
}

.info-col .detail-item .label {
    font-size: 1rem;
    color: var(--content-secondary);
    font-weight: 500;
    display: block;
}

.info-col .detail-item .value {
    font-size: 1.5rem;
    color: var(--content-primary);
    font-weight: 600;
    display: block;
}

.info-col .detail-item[data-field-type="financial"] .value {
    color: var(--success-fill);
    font-size: 1.5rem;
}

.info-col .detail-item[data-field-type="balance-outstanding"] .value {
    color: var(--critical-fill);
    font-size: 1.5rem;
    font-weight: 700;
}

.info-col .detail-item[data-field-type="contact"] .value {
    color: var(--info-fill);
    cursor: pointer;
    text-decoration: underline;
    text-decoration-style: dotted;
}

.info-col .detail-item[data-field-type="contact"] .value:hover {
    text-decoration-style: solid;
}

.info-col .detail-item[data-field-type="date"] .value {
    color: var(--core-dark-accent);
}

.info-col .value.updated {
    animation: highlight 1s ease;
}

@keyframes highlight {
    0% {
        background-color: var(--warning-surface);
        transform: scale(1.05);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

.info-col .activity-item {
    background: rgba(255, 255, 255, 0.95);
}

.info-col .accordion-button {
    background: linear-gradient(135deg, var(--core-primary) 0%, var(--core-dark-accent) 100%);
    color: var(--content-inverse);
    font-weight: 600;
    border: none;
}

.info-col .accordion-button:not(.collapsed) {
    background: linear-gradient(135deg, var(--core-dark-accent) 0%, var(--core-primary) 100%);
    color: var(--content-inverse);
}

.info-col .accordion-button:focus {
    box-shadow: none;
}

@media (max-width: 768px) {
    .info-col {
        padding: 0.5rem;
    }

    .info-col .detail-wrapper {
        grid-template-columns: 1fr;
    }

    .info-col .__title {
        font-size: 1.25rem;
    }
}

.info-col.loading {
    position: relative;
    pointer-events: none;
}

.info-col.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 4px solid var(--neutral-200);
    border-top: 4px solid var(--core-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.info-col .detail-item[data-field-type="financial"]::after,
.info-col .detail-item[data-field-type="contact"]::after,
.info-col .detail-item[data-field-type="date"]::after {
    display:none;
}

.info-col .detail-item[data-field-type="financial"]::after {
    content: '\f155';
}

.info-col .detail-item[data-field-type="contact"]::after {
    content: '\f0e0';
}

.info-col .detail-item[data-field-type="date"]::after {
    content: '\f073';
}

.info-col {
    scroll-behavior: smooth;
    max-height: calc(100vh - 100px);
    overflow-y: auto;
}

.info-col::-webkit-scrollbar {
    width: 8px;
}

.info-col::-webkit-scrollbar-track {
    background: var(--neutral-100);
    border-radius: 4px;
}

.info-col::-webkit-scrollbar-thumb {
    background: var(--core-primary);
    border-radius: 4px;
}

.info-col::-webkit-scrollbar-thumb:hover {
    background: var(--core-dark-accent);
} 
