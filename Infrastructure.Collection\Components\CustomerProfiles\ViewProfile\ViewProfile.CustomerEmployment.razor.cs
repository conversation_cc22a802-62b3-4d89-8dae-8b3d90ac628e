﻿using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Specification;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;
public partial class ViewProfile
{
    private DataTable<CustomerEmploymentVM> customerEmploymentsTable;
    private ColumnList customerEmploymentTableDefinition;
    private string AddCustomerEmploymentModal => "AddCustomerEmploymentModal";
    private string EditCustomerEmploymentModal => "EditCustomerEmploymentModal";

    private CustomerEmployment AddCustomerEmploymentModel { get; set; } = new();
    private CustomerEmployment EditCustomerEmploymentModel { get; set; } = new();

    private void SetupCustomerEmployment()
    {
        customerEmploymentTableDefinition = CrudService.GetTableDefinition<CustomerEmployment, CustomerEmploymentVM>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerEmploymentPermission),
            HasEdit = HasClaim(ManageCustomersNavigation.CustomerProfileViewEditCustomerEmploymentPermission),
        });

        customerEmploymentTableDefinition.TopActionButtons.Add(new TopActionButton("Add", ModalName: AddCustomerEmploymentModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddCustomerEmploymentPermission)));

        customerEmploymentsTable.SetTableDefinition(customerEmploymentTableDefinition);

    }

    private ValueTask<TypedBasePageList<CustomerEmploymentVM>> LoadCustomerEmployment(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerEmployment>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, CustomerEmploymentVM.Mapping, ct);
    }

    private ValueTask SubmitNewCustomerEmployment() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddCustomerEmploymentPermission, AddCustomerEmploymentModal, async () =>
    {
        AddCustomerEmploymentModel.ProfileId = Data.Id;
        uow.Db.Insert(AddCustomerEmploymentModel);
        await uow.SaveAsync(Cancel);

        return true;
    }, () =>
    {
        AddCustomerEmploymentModel = new();
        StateHasChanged();
        return customerEmploymentsTable.Refresh();
    });

    private ValueTask StartEditCustomerEmployment(CustomerEmploymentVM data, CancellationToken ct) => BaseEdit(ManageCustomersNavigation.CustomerProfileViewEditCustomerEmploymentPermission, EditCustomerEmploymentModal, () =>
    {
        EditCustomerEmploymentModel = data.Get(Data.Id);
        return Task.CompletedTask;
    }, ct);

    private ValueTask SubmitEditCustomerEmployment() => BaseSaveEdit(null, EditCustomerEmploymentModal, async () =>
    {
        uow.Db.Update(EditCustomerEmploymentModel);
        await uow.SaveAsync(Cancel);
        return true;
    }, customerEmploymentsTable.Refresh);

    private ValueTask<bool> DeleteCustomerEmployment(CustomerEmploymentVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerEmploymentPermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerEmployment>(x => x.Id == data.Id, ct);
        return result > 0;
    }, refresh);

}
