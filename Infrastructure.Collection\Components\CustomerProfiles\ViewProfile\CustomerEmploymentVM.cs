﻿using System.ComponentModel;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public sealed class CustomerEmploymentVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<CustomerEmployment, CustomerEmploymentVM>> Mapping = data => new()
    {
        Id = data.Id,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId,
        Source = data.Source,
        SalaryFrequency = data.SalaryFrequency,
        PayDay = data.PayDay,
        PayDate = data.PayDate,
        Occupation = data.Occupation,
        Note = data.Note,
        MonthlySalaryAmount = data.MonthlySalaryAmount,
        EmploymentType = data.EmploymentType,
        EmploymentStatus = data.EmploymentStatus,
        EmployerName = data.EmployerName,
        AnnualSalaryAmount = data.AnnualSalaryAmount,
        EmployedEndDate = data.EmployedEndDate,
        EmployedStartDate = data.EmployedStartDate
    };

    public CustomerEmployment Get(string profileId) => new()
    {
        Id = Id,
        ProfileId = profileId,
        AnnualSalaryAmount = AnnualSalaryAmount,
        EmployedEndDate = EmployedEndDate,
        EmployedStartDate = EmployedStartDate,
        EmployerName = EmployerName,
        EmploymentStatus = EmploymentStatus,
        EmploymentType = EmploymentType,
        MonthlySalaryAmount = MonthlySalaryAmount,
        Note = Note,
        Occupation = Occupation,
        PayDate = PayDate,
        PayDay = PayDay,
        SalaryFrequency = SalaryFrequency,
        Source = Source,
    };

    public string EmploymentStatus { get; set; }
    public string EmploymentType { get; set; }
    public string EmployerName { get; set; }
    public string Occupation { get; set; }
    public decimal MonthlySalaryAmount { get; set; }
    public decimal AnnualSalaryAmount { get; set; }
    public DateOnly? EmployedStartDate { get; set; }
    public DateOnly? EmployedEndDate { get; set; }
    public string SalaryFrequency { get; set; }
    public string Note { get; set; }
    public string Source { get; set; }
    public DateOnly? PayDate { get; set; }
    public string PayDay { get; set; }
}


public enum CustomerEmploymentStatus
{
    Active,
    Inactive
}

public enum CustomerEmploymentType
{
    FullTime,
    PartTime,
    Contract,
    Temporary,
    Freelance,
    Internship,
    SelfEmployed,
    Retired,
    Other
}

public enum CustomerEmploymentSalaryFrequency
{
    Monthly,
    BiWeekly,
    Weekly,
    Annually,
    LastFriday,
    LastWorkingDay,
    [Description("4Weekly")]
    FourWeekly,
    Other
}