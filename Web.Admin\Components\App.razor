﻿@using Radzen.Blazor

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />


    <link rel="stylesheet" type="text/css" href="@Assets["app.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["Web.Admin.styles.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["css/reboot.min.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["css/bootstrap.min.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["css/main.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["css/main-responsive.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["lib/animate.min.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["lib/font-awesome/css/font-awesome.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["lib/daterangepicker/daterangepicker-bs3.css"]" />
    <RadzenTheme Theme="standard" @rendermode="RenderModeForPage" />
    <link rel="stylesheet" type="text/css" href="@Assets["css/site.css"]" />
    <link rel="icon" type="image/x-icon" sizes="32x32" href="@Assets["favicon.ico"]" />
    <script src="@Assets["js/vendor/feather.min.js"]"></script>
    <script src="@Assets["js/vendor/qrcode.min.js"]"></script>

    <meta http-equiv="Content-Security-Policy"
          content="base-uri 'self';
               img-src data: https:;
               object-src 'none';
               upgrade-insecure-requests;">

    <ImportMap />
    <HeadOutlet @rendermode="RenderModeForPage" />
</head>

<body>
    <Routes @rendermode="RenderModeForPage" />
    <div id="reconnect-modal" style="display: none;"></div>
    <script src="_framework/blazor.web.js" autostart="false"></script>
    

    <script src="@Assets["boot.js"]"></script>
    <script src="@Assets["js/vendor/jquery-1.11.2.min.js"]"></script>
    <script src="@Assets["js/vendor/bootstrap.bundle.min.js"]"></script>
    <script src="@Assets["lib/daterangepicker/moment.min.js"]"></script>
    <script src="@Assets["lib/daterangepicker/daterangepicker.js"]"></script>
    <script src="@Assets["js/main.js"]"></script>

    <script src="_content/Radzen.Blazor/Radzen.Blazor.js?v=@(typeof(Radzen.Colors).Assembly.GetName().Version)"></script>
    <script> 
        window.blazorExtensions = {
            GetBrowserTimeZone: function () {
                const options = Intl.DateTimeFormat().resolvedOptions();
                return options.timeZone;
            },
            RunFeather: function () {
                feather.replace();

                $('input.hasDate[type=text]').daterangepicker({
                    singleDatePicker: true,
                    showDropdowns: true,
                    minYear: 2021,
                    maxYear: parseInt(moment().format('YYYY'), 10),
                    format: '@DateTimeExtensions.JSSHORTDATEFORMAT'
                });
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl)
                });
            },
            OnNavigate: function() {
                $('[data-toggle="tooltip"], .tooltip').tooltip("hide");
                $('.modal').modal('hide');
                if ($('.modal-backdrop'))
                    $('.modal-backdrop').remove();
            },
            OpenModal: function (modalId) {
                $("#" + modalId).modal('show');
            },
            CloseModal: function (modalId) {
                $("#" + modalId).modal('hide');
                if ($("#buttonSpinner")) {
                    $("#buttonSpinner").remove();
                    $("#" + modalId + "_Button").removeClass("disabled");
                }
            },
            TriggerFileDownload: function (fileName, url) {
                const anchorElement = document.createElement('a');
                anchorElement.href = url;
                anchorElement.download = fileName ?? '';
                anchorElement.click();
                anchorElement.remove();
            },
            CopyToClipboard: function (text, id) {
                navigator.clipboard.writeText(text).then(() => {
                    if (id) {
                        const element = $('#' + id)[0];
                        if (element) {
                            const originalText = element.textContent;
                            element.textContent = 'Copied!';
                            element.style.color = '#27ae60';

                            setTimeout(() => {
                                element.textContent = originalText;
                                element.style.color = '';
                            }, 1500);
                        }
                    }
                });
            },
            AnimateNumber: function (id, start, end, duration) {
                const element = $('#' + id)[0];
                if (!element) return;

                const range = end - start;
                const startTime = performance.now();

                function updateNumber(currentTime) {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    const current = start + (range * easeOutCubic(progress));
                    element.textContent = formatCurrency(current);

                    if (progress < 1) {
                        requestAnimationFrame(updateNumber);
                    }
                }

                function easeOutCubic(t) {
                    return 1 - Math.pow(1 - t, 3);
                }

                function formatCurrency(value) {
                    return new Intl.NumberFormat('en-GB', {
                        style: 'currency',
                        currency: 'GBP'
                    }).format(value);
                }

                requestAnimationFrame(updateNumber);
            }
        }
    </script>
</body>

</html> 
 
 
@code {
    [CascadingParameter]
    private HttpContext HttpContext { get; set; } 

    private IComponentRenderMode RenderModeForPage => HttpContext.AcceptsInteractiveRouting() ? InteractiveServer : null;
}
