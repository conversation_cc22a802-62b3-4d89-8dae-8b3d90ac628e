﻿using Coravel.Queuing.Interfaces;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.FileManagement;
using Microsoft.AspNetCore.Components;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    [Inject] NavigationManager NavigationManager { get; set; }
    [Inject] GeneralGenericCrudVMService CrudService { get; set; }
    [Inject] HttpClient HttpClient { get; set; }
    [Inject] DefaultAppConfig Config { get; set; }
    [Inject] IQueue Queue { get; set; }
    [Inject] IFileManagementService FileManagementService { get; set; }


    [Parameter]
    public string ProfileId { get; set; }

    private string Title { get; set; } = "View Customer Profile";

    private IUnitofWork uow;

    private CustomerProfileVM Data { get; set; } = new();

    private List<CustomerDropDownPlacementVM> placementsDropDown = [];
    private string timeZone;
    protected override void OnInitialized()
    {
        uow = CrudService.Uow;
        base.OnInitialized();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await Load();
            await JSRuntime.RunFeather(Cancel);
            timeZone = await JSRuntime.GetBrowserTimezone(Cancel);
            StateHasChanged();
        }
    }

    private async Task LoadProfile()
    {
        Data = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerProfileVM>.Where(x => x.Id == ProfileId).Select(CustomerProfileVM.Mapping), Cancel);
        totalPlacements = Data.TotalPlacements;
    }

    private async Task Load()
    {
        await LoadProfile();
        placementsDropDown = await uow.Db.ManySelectAsync(Query<Placement, CustomerDropDownPlacementVM>.Where(x => x.ProfileId == ProfileId).Select(CustomerDropDownPlacementVM.Mapping), Cancel);

        SetupPlacements();
        SetupContact();
        SetupCustomerDocuments();
        SetupMessageTable();
        SetupFlag();
        SetupStatusChange();
        SetupHold();
        SetupCustomerEarningsArrestment();
        SetupCustomerLegal();
        SetupCustomerEmployment();
        SetupCustomerTrace();
        SetupCustomerFee();
        SetupNotesConfig();
        SetupPtpConfig();

        await SetupDiscountConfig();
        await LoadTask();
    }
}
