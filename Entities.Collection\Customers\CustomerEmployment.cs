﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Customers;

public class CustomerEmployment : BaseEntityWithIdentityId<CustomerEmployment>
{
    [DbGuid]
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public string EmploymentStatus { get; set; }
    public string EmploymentType { get; set; }
    public string EmployerName { get; set; }
    public string Occupation { get; set; }
    public decimal MonthlySalaryAmount { get; set; }
    public decimal AnnualSalaryAmount { get; set; }
    public DateOnly? EmployedStartDate { get; set; }
    public DateOnly? EmployedEndDate { get; set; }
    public string SalaryFrequency { get; set; }
    public string Note { get; set; }
    public string Source { get; set; }
    public DateOnly? PayDate { get; set; }
    public string PayDay { get; set; }
}
