﻿using LendQube.Infrastructure.Core.Navigation;

namespace LendQube.Infrastructure.Collection.Navigation;

public class ManageCustomersNavigation : INavigationDescriptor, INavigatorHasPermissions
{
    public bool IsDisabled { get; set; } = false;
    public const string GroupName = "Customers";
    public void PrepareNavigator()
    {
        var navs = new NavigatorVM
        {
            Name = "Customers",
            Icon = "user",
            Permission = CustomerProfileIndexPermission,
            Controller = GroupName,
            Url = "customers/profile",
            SubNavigation =
            [
                new NavigatorVM { Name = "Manage Profile", Icon = "user", Permission = CustomerProfileIndexPermission, Url = "customers/profile", Controller = GroupName },
                new NavigatorVM { Name = "All Transactions", Icon = "user", Permission = TransactionsIndexPermission, Url = "customers/transactions", Controller = GroupName },
                new NavigatorVM { Name = "Setup Flags", Icon = "user", Permission = CustomerFlagSetupIndexPermission, Url = "customers/flagsetup", Controller = GroupName },
                new NavigatorVM { Name = "Note Templates", Icon = "user", Permission = CustomerNoteTemplateIndexPermission, Url = "customers/notetemplates", Controller = GroupName },
                new NavigatorVM { Name = "Hold Duration Setup", Icon = "user", Permission = HoldDurationSetupIndexPermission, Url = "customers/holddurationsetup", Controller = GroupName },
                new NavigatorVM { Name = "Hold Setup", Icon = "user", Permission = HoldSetupIndexPermission, Url = "customers/holdsetup", Controller = GroupName },
                new NavigatorVM { Name = "Status Change Reason Setup", Icon = "user", Permission = StatusChangeReasonSetupIndexPermission, Url = "customers/accountstatuschangereasonsetup", Controller = GroupName },
                new NavigatorVM { Name = "Discount Setup", Icon = "user", Permission = DiscountSetupIndexPermission, Url = "customers/discountsetup", Controller = GroupName },
                new NavigatorVM { Name = "Apply Auto Discount", Icon = "user", Permission = AutoDiscountConfigIndexPermission, Url = "customers/autodiscountsetup", Controller = GroupName },
            ]
        };

        Navigator.SetupModuleNavigation(NavigationOrder.ManageCustomerIndex, GroupName, navs);
    }

    public void PreparePermissionDescriptions()
    {
        #region Profile
        Navigator.PermissionDescription[CustomerProfileIndexPermission] = $"Can view customers and grants access to {GroupName}";
        Navigator.PermissionDescription[CustomerProfileViewPermission] = "Can view customer profile";
        Navigator.PermissionDescription[CustomerProfileDeletePermission] = "Can delete customer profile";

        Navigator.PermissionDescription[CustomerProfileViewAddNotePermission] = "Can add customer notes";
        Navigator.PermissionDescription[CustomerProfileViewEditNotePermission] = "Can edit customer notes";
        Navigator.PermissionDescription[CustomerProfileViewDeleteNotePermission] = "Can delete customer notes";

        Navigator.PermissionDescription[CustomerProfileViewAddPtpPermission] = "Can add customer promise to pay";
        Navigator.PermissionDescription[CustomerProfileViewDeletePtpPermission] = "Can delete customer promise to pay";

        Navigator.PermissionDescription[CustomerProfileViewAddContactPermission] = "Can add customer contact";
        Navigator.PermissionDescription[CustomerProfileViewUpdateContactLoginPermission] = "Can update customer login with contact details";
        Navigator.PermissionDescription[CustomerProfileViewDeleteContactPermission] = "Can delete customer contact";

        Navigator.PermissionDescription[CustomerProfileViewAddFlagPermission] = "Can add customer flag";
        Navigator.PermissionDescription[CustomerProfileViewDeleteFlagPermission] = "Can delete customer flag";

        Navigator.PermissionDescription[CustomerProfileViewAddDiscountPermission] = "Can add customer discount";

        Navigator.PermissionDescription[CustomerProfileToggleBlacklistCustomerPermission] = "Can blacklist customer profile";

        Navigator.PermissionDescription[CustomerProfileResetOtpPermission] = "Can reset customer otp";
        Navigator.PermissionDescription[CustomerProfileTakePaymentPermission] = "Can take customer payment";
        Navigator.PermissionDescription[CustomerProfileCreateSchedulePermission] = "Can create customer repayment schedule";
        Navigator.PermissionDescription[CustomerProfileDeleteSchedulePermission] = "Can delete customer repayment schedule";
        Navigator.PermissionDescription[CustomerProfileUpdateSchedulePermission] = "Can update customer repayment schedule";
        Navigator.PermissionDescription[CustomerProfileClearIAndEPermission] = "Can clear customer income and expenditure";
        Navigator.PermissionDescription[CustomerProfileResetAmendSchedulePermission] = "Can lift customer amend restriction";
        Navigator.PermissionDescription[CustomerProfileChangePlacementStatusPermission] = "Can change placement status";
        Navigator.PermissionDescription[CustomerProfileAddHoldPermission] = "Can add hold to customer account";
        Navigator.PermissionDescription[CustomerProfileModifyHoldPermission] = "Can modify hold to customer account";
        Navigator.PermissionDescription[CustomerProfileDeleteHoldPermission] = "Can remove hold to customer account";

        Navigator.PermissionDescription[CustomerProfileSendCustomMessagePermission] = "Can send custom message to customer";
        Navigator.PermissionDescription[CustomerProfileSendMessageWithSelectedContainerPermission] = "Can send message with selected container to customer";

        Navigator.PermissionDescription[CustomerProfileViewAddCustomerDocumentPermission] = "Can add customer document";
        Navigator.PermissionDescription[CustomerProfileViewUpdateCustomerDocumentPermission] = "Can update customer document";
        Navigator.PermissionDescription[CustomerProfileViewDeleteCustomerDocumentPermission] = "Can delete customer document";
        #endregion

        #region Transactions
        Navigator.PermissionDescription[TransactionsIndexPermission] = "Can view all transactions";
        Navigator.PermissionDescription[TransactionsViewPermission] = "Can view transaction details";
        #endregion

        #region Setup
        Navigator.PermissionDescription[CustomerFlagSetupIndexPermission] = "Can view customer flag setup";
        Navigator.PermissionDescription[CustomerFlagSetupCreatePermission] = "Can create customer flag setup";
        Navigator.PermissionDescription[CustomerFlagSetupEditPermission] = "Can edit customer flag setup";
        Navigator.PermissionDescription[CustomerFlagSetupDeletePermission] = "Can delete customer flag setup";

        Navigator.PermissionDescription[CustomerNoteTemplateIndexPermission] = "Can view customer note templates";
        Navigator.PermissionDescription[CustomerNoteTemplateCreatePermission] = "Can create customer note templates";
        Navigator.PermissionDescription[CustomerNoteTemplateEditPermission] = "Can edit customer note templates";
        Navigator.PermissionDescription[CustomerNoteTemplateDeletePermission] = "Can delete customer note templates";

        Navigator.PermissionDescription[HoldDurationSetupIndexPermission] = "Can view account hold duration setup";
        Navigator.PermissionDescription[HoldDurationSetupCreatePermission] = "Can create account hold duration setup";
        Navigator.PermissionDescription[HoldDurationSetupEditPermission] = "Can edit account hold duration setup";
        Navigator.PermissionDescription[HoldDurationSetupDeletePermission] = "Can delete account hold duration setup";

        Navigator.PermissionDescription[HoldSetupIndexPermission] = "Can view account hold setup";
        Navigator.PermissionDescription[HoldSetupCreatePermission] = "Can create account hold setup";
        Navigator.PermissionDescription[HoldSetupEditPermission] = "Can edit account hold setup";
        Navigator.PermissionDescription[HoldSetupDeletePermission] = "Can delete account hold setup";

        Navigator.PermissionDescription[DiscountSetupIndexPermission] = "Can view account discount setup";
        Navigator.PermissionDescription[DiscountSetupCreatePermission] = "Can create account discount setup";
        Navigator.PermissionDescription[DiscountSetupEditPermission] = "Can edit account discount setup";
        Navigator.PermissionDescription[DiscountSetupDeletePermission] = "Can delete account discount setup";

        Navigator.PermissionDescription[AutoDiscountConfigIndexPermission] = "Can view auto account discount";
        Navigator.PermissionDescription[AutoDiscountConfigCreatePermission] = "Can create auto account discount";
        Navigator.PermissionDescription[AutoDiscountConfigEditPermission] = "Can edit auto account discount";
        Navigator.PermissionDescription[AutoDiscountConfigDeletePermission] = "Can delete auto account discount";

        Navigator.PermissionDescription[StatusChangeReasonSetupIndexPermission] = "Can view account status change reason setup";
        Navigator.PermissionDescription[StatusChangeReasonSetupCreatePermission] = "Can create account status change reason setup";
        Navigator.PermissionDescription[StatusChangeReasonSetupEditPermission] = "Can edit account status change reason setup";
        Navigator.PermissionDescription[StatusChangeReasonSetupDeletePermission] = "Can delete account status change reason setup";
        #endregion

    }

    #region Profile


    public const string CustomerProfileIndexPermission = "Permission.CustomerProfile.Index";
    public const string CustomerProfileDeletePermission = "Permission.CustomerProfile.Delete";
    public const string CustomerProfileViewPermission = "Permission.CustomerProfile.View";

    public const string CustomerProfileViewAddNotePermission = "Permission.CustomerProfile.AddNote";
    public const string CustomerProfileViewEditNotePermission = "Permission.CustomerProfile.EditNote";
    public const string CustomerProfileViewDeleteNotePermission = "Permission.CustomerProfile.DeleteNote";

    public const string CustomerProfileViewAddPtpPermission = "Permission.CustomerProfile.AddPtp";
    public const string CustomerProfileViewDeletePtpPermission = "Permission.CustomerProfile.DeletePtp";

    public const string CustomerProfileViewAddContactPermission = "Permission.CustomerProfile.AddContact";
    public const string CustomerProfileViewUpdateContactLoginPermission = "Permission.CustomerProfile.UpdateContactLogin";
    public const string CustomerProfileViewDeleteContactPermission = "Permission.CustomerProfile.DeleteContact";

    public const string CustomerProfileViewAddFlagPermission = "Permission.CustomerProfile.AddFlag";
    public const string CustomerProfileViewDeleteFlagPermission = "Permission.CustomerProfile.DeleteFlag";


    public const string CustomerProfileViewAddDiscountPermission = "Permission.CustomerProfile.AddDiscount";

    public const string CustomerProfileToggleBlacklistCustomerPermission = "Permission.CustomerProfile.ToggleBlacklistCustomer";

    public const string CustomerProfileResetOtpPermission = "Permission.CustomerProfile.ResetOtp";
    public const string CustomerProfileDeleteSchedulePermission = "Permission.CustomerProfile.DeleteSchedule";
    public const string CustomerProfileUpdateSchedulePermission = "Permission.CustomerProfile.UpdateSchedule";
    public const string CustomerProfileCreateSchedulePermission = "Permission.CustomerProfile.CreateSchedule";
    public const string CustomerProfileTakePaymentPermission = "Permission.CustomerProfile.TakePayment";
    public const string CustomerProfileClearIAndEPermission = "Permission.CustomerProfile.ClearIAndE";
    public const string CustomerProfileResetAmendSchedulePermission = "Permission.CustomerProfile.ResetAmendSchedule";
    public const string CustomerProfileChangePlacementStatusPermission = "Permission.CustomerProfile.ChangePlacementStatus";
    public const string CustomerProfileAddHoldPermission = "Permission.CustomerProfile.AddHold";
    public const string CustomerProfileModifyHoldPermission = "Permission.CustomerProfile.ModifyHold";
    public const string CustomerProfileDeleteHoldPermission = "Permission.CustomerProfile.DeleteHold";

    public const string CustomerProfileSendCustomMessagePermission = "Permission.CustomerProfile.SendMessage";
    public const string CustomerProfileSendMessageWithSelectedContainerPermission = "Permission.CustomerProfile.SendMessageWithSelectedContainer";

    public const string CustomerProfileViewAddCustomerDocumentPermission = "Permission.CustomerProfile.AddCustomerDocument";
    public const string CustomerProfileViewUpdateCustomerDocumentPermission = "Permission.CustomerProfile.UpdateCustomerDocument";
    public const string CustomerProfileViewDeleteCustomerDocumentPermission = "Permission.CustomerProfile.DeleteCustomerDocument";
    #endregion

    #region Transactions
    public const string TransactionsIndexPermission = "Permission.Transactions.Index";
    public const string TransactionsViewPermission = "Permission.Transactions.View";
    #endregion

    #region Setup
    public const string CustomerFlagSetupIndexPermission = "Permission.CustomerFlagSetup.Index";
    public const string CustomerFlagSetupCreatePermission = "Permission.CustomerFlagSetup.Create";
    public const string CustomerFlagSetupEditPermission = "Permission.CustomerFlagSetup.Edit";
    public const string CustomerFlagSetupDeletePermission = "Permission.CustomerFlagSetup.Delete";

    public const string CustomerNoteTemplateIndexPermission = "Permission.CustomerNoteTemplate.Index";
    public const string CustomerNoteTemplateCreatePermission = "Permission.CustomerNoteTemplate.Create";
    public const string CustomerNoteTemplateEditPermission = "Permission.CustomerNoteTemplate.Edit";
    public const string CustomerNoteTemplateDeletePermission = "Permission.CustomerNoteTemplate.Delete";

    public const string HoldDurationSetupIndexPermission = "Permission.HoldDurationSetup.Index";
    public const string HoldDurationSetupCreatePermission = "Permission.HoldDurationSetup.Create";
    public const string HoldDurationSetupEditPermission = "Permission.HoldDurationSetup.Edit";
    public const string HoldDurationSetupDeletePermission = "Permission.HoldDurationSetup.Delete";

    public const string HoldSetupIndexPermission = "Permission.HoldSetup.Index";
    public const string HoldSetupCreatePermission = "Permission.HoldSetup.Create";
    public const string HoldSetupEditPermission = "Permission.HoldSetup.Edit";
    public const string HoldSetupDeletePermission = "Permission.HoldSetup.Delete";

    public const string DiscountSetupIndexPermission = "Permission.DiscountSetup.Index";
    public const string DiscountSetupCreatePermission = "Permission.DiscountSetup.Create";
    public const string DiscountSetupEditPermission = "Permission.DiscountSetup.Edit";
    public const string DiscountSetupDeletePermission = "Permission.DiscountSetup.Delete";

    public const string StatusChangeReasonSetupIndexPermission = "Permission.StatusChangeReasonSetup.Index";
    public const string StatusChangeReasonSetupCreatePermission = "Permission.StatusChangeReasonSetup.Create";
    public const string StatusChangeReasonSetupEditPermission = "Permission.StatusChangeReasonSetup.Edit";
    public const string StatusChangeReasonSetupDeletePermission = "Permission.DiscountSetup.Delete";

    public const string AutoDiscountConfigIndexPermission = "Permission.AutoDiscountConfig.Index";
    public const string AutoDiscountConfigCreatePermission = "Permission.AutoDiscountConfig.Create";
    public const string AutoDiscountConfigEditPermission = "Permission.AutoDiscountConfig.Edit";
    public const string AutoDiscountConfigDeletePermission = "Permission.AutoDiscountConfig.Delete";
    #endregion
}
