﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Customers;
public class CustomerEarningsArrestment : BaseEntityWithIdentityId<CustomerEarningsArrestment>
{
    [DbGuid]
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }

    public string SiliconReference { get; set; }
    public string CaseReference { get; set; }
    public string AEReference { get; set; }
    public string Court { get; set; }
    public string LatestUpdate { get; set; }
    public DateOnly? LatestUpdateDate { get; set; }
    public string LatestUpdateDescription { get; set; }
    public string ActionRequired { get; set; }
    public DateOnly? ScannedDate { get; set; }

}
