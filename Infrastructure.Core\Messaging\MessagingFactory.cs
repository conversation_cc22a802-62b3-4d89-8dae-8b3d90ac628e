﻿using System.Collections.Concurrent;
using System.Text;
using LendQube.Entities.Core.BaseUser;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Messaging;

internal sealed class MessagingFactory(IUnitofWork uow, IMessageTemplateKeyProvider templateKeyProvider, DefaultAppConfig config, MessageRouter router, HttpClient httpClient)
{
    public async Task SendMessage(long id, CancellationToken ct)
    {
        var query = Query<MessageLog>
            .Where(x => x.Id == id)
            .Include(x =>
            x.Include(y => y.MessageLogEntries)
                .ThenInclude(y => y.Config))
            .Track();

        if (!config.DeployState.IsDemo)
            query.AndWhere(x => x.Status == MessageStatus.Queued);

        var message = await uow.Db.OneAsync(query, ct);

        if (message == null)
            return;

        uow.Db.Insert(new MessageLogActivity { MessageLogId = id, Title = "Dispatch", Activity = "Starting message preparation", CreatedByUser = message.CreatedByUser, CreatedByUserId = message.CreatedByUserId });

        var preparedMessages = new ConcurrentDictionary<MessageChannel, List<PreparedMessageVM>>();
        var systemKeys = EnumExtensions.GetEnumNames<MessageTemplateSystemTags>();

        var recipientsData = await GetRecipientsDataFromDb(message, ct);

        var disallowedDomains = config.DisallowedEmails.NoMessageDomains.Split(',', StringSplitOptions.TrimEntries);
        await Parallel.ForEachAsync(message.MessageLogEntries, new ParallelOptions { MaxDegreeOfParallelism = message.MessageLogEntries.Count, CancellationToken = ct },
            async (item, ct) =>
            {
                var preparedMessage = new PreparedMessageVM
                {
                    MessageId = item.MessageLogId,
                    Channel = item.Channels,
                    Subject = config.DeployState.IsDemo ? $"[Dev] {item.Subject}" : item.Subject,
                    SenderEmail = string.IsNullOrEmpty(item.SenderEmail) ? item.Config?.SenderEmail : item.SenderEmail,
                    SenderName = string.IsNullOrEmpty(item.SenderName) ? item.Config?.SenderName : item.SenderName,
                    CreatedByUser = message.CreatedByUser,
                    CreatedByUserId = message.CreatedByUserId,
                };

                PrepareTextMessage(preparedMessage, item);
                await PrepareHtmlMessage(preparedMessage, item, ct);

                var systemTags = item.Keys.Where(x => systemKeys.Contains(x));

                preparedMessage.CopiedIn = item.CopiedIn;

                preparedMessage.HasSeparateTemplateValues = !item.Keys.IsNullOrEmpty()
                    || (!systemTags.IsNullOrEmpty() && !systemTags.Any(x => x.Equals(MessageTemplateSystemTags.Body.ToString(), StringComparison.OrdinalIgnoreCase) || x.Equals(MessageTemplateSystemTags.Logo.ToString(), StringComparison.OrdinalIgnoreCase)))
                    || item.Recipients.Any(x => !x.Attachments.IsNullOrEmpty())
                    || item.Recipients.Any(x => !x.CopiedIn.IsNullOrEmpty());


                preparedMessage.Data = RecipientWithDataUnionQuery(item.Recipients, recipientsData, systemKeys, config.Url.Logo, systemTags, disallowedDomains);

                if (preparedMessages.TryGetValue(item.Channels, out var channelMessages))
                {
                    channelMessages.Add(preparedMessage);
                }
                else
                {
                    preparedMessages.TryAdd(item.Channels, [preparedMessage]);
                }
            });

        uow.Db.Insert(new MessageLogActivity { MessageLogId = id, Title = "Dispatch", Activity = "Finish message preparation", CreatedByUser = message.CreatedByUser, CreatedByUserId = message.CreatedByUserId });

        message.Status = await router.RouteMessages(uow, id, preparedMessages, ct);

        message.AttemptCount += 1;

        uow.Db.Insert(new MessageLogActivity
        {
            MessageLogId = id,
            Title = "Dispatch",
            Activity = $"Finish message dispatch with status {message.Status.GetDisplayName()}",
            CreatedByUser = message.CreatedByUser,
            CreatedByUserId = message.CreatedByUserId
        });
        await uow.SaveAsync(ct);
    }


    private async Task<ParallelQuery<MessageRecipientUserDataVM>> GetRecipientsDataFromDb(MessageLog message, CancellationToken ct)
    {
        var receiversWithUserId = message.MessageLogEntries.SelectMany(entry => entry.Recipients.Where(x => !string.IsNullOrEmpty(x.UserId) && !x.MessagingGroupId.HasValue).Select(x => x.UserId)).Distinct();
        var receiversFromMessagingGroup = message.MessageLogEntries.SelectMany(entry => entry.Recipients.Where(x => string.IsNullOrEmpty(x.UserId) && x.MessagingGroupId.HasValue).Select(x => x.MessagingGroupId.Value)).Distinct();

        var userDataFromUsers = await uow.Db.ManySelectAsync(Query<ApplicationUser, MessageRecipientUserDataVM>
           .Where(x => receiversWithUserId.Contains(x.UserName) && (!string.IsNullOrEmpty(x.Email) || !string.IsNullOrEmpty(x.PhoneNumber)))
           .Select(MessageRecipientUserDataVM.UserMapping), ct);

        StringBuilder logs = new();

        if (!userDataFromUsers.IsNullOrEmpty())
        {
            logs.AppendLine($"Adding {userDataFromUsers.Count} recipients from user data");
        }

        var userDataFromMessagingGroup = await uow.Db.ManySelectAsync(Query<MessagingGroupEntry, MessageRecipientUserDataVM>
            .Where(x => receiversFromMessagingGroup.Contains(x.MessagingGroupId) && (x.Emails.Any() || x.PhoneNumbers.Any()))
            .Select(MessageRecipientUserDataVM.MessagingGroupMapping), ct);

        if (!userDataFromMessagingGroup.IsNullOrEmpty())
        {
            logs.AppendLine($"Adding {userDataFromMessagingGroup.Count} recipients from groups: {string.Join(", ", userDataFromMessagingGroup.Select(x => x.GroupName))}");
        }

        var queries = await uow.Db.ManySelectAsync(Query<MessagingGroupQuery, MessageRecipientUserDataVM>
            .Where(x => receiversFromMessagingGroup.Contains(x.MessagingGroupId) && !string.IsNullOrEmpty(x.SenderQuery))
            .Select(MessageRecipientUserDataVM.MessagingGroupQueryMapping), ct);

        if (queries.Count > 0)
        {
            foreach (var query in queries)
            {
                var queryResult = await uow.Context.Database.SqlQueryRaw<Guid>(query.Query).ToListAsync(ct);
                var userIdsFromQueries = queryResult.Select(x => x.ToString()).ToList();

                var queryDataFromUsers = await uow.Db.ManySelectAsync(Query<ApplicationUser, MessageRecipientUserDataVM>
                   .Where(x => userIdsFromQueries.Contains(x.UserName) && (!string.IsNullOrEmpty(x.Email) || !string.IsNullOrEmpty(x.PhoneNumber)))
                   .Select(MessageRecipientUserDataVM.UserMapping), ct);

                var dataToUpdate = message.MessageLogEntries.FirstOrDefault(x => x.Recipients.Any(y => y.MessagingGroupId == query.MessagingGroupId));
                var groupRecipient = dataToUpdate.Recipients.FirstOrDefault();
                dataToUpdate.Recipients = null;
                await uow.SaveAsyncWithTracking(ct);

                queryDataFromUsers.ForEach(x => x.MessagingGroupId = groupRecipient.MessagingGroupId);

                var sourcedTemplateKeysAndValues = await templateKeyProvider.GetKeysWithValues([.. queryDataFromUsers.Select(x => x.UserId)], [.. groupRecipient.TemplateValues?.Select(x => x.Key)], ct);

                dataToUpdate.Recipients = queryDataFromUsers
                    .Select(x => new MessageRecipient
                    {
                        UserId = x.UserId,
                        MessagingGroupId = x.MessagingGroupId,
                        Attachments = groupRecipient.Attachments?.Select(x => x.DeepCopy()).ToList(),
                        CopiedIn = groupRecipient.CopiedIn?.Select(x => x.DeepCopy()).ToList(),
                        TemplateValues = sourcedTemplateKeysAndValues.GetValueOrDefault(x.UserId) ?? []
                    }).ToList();

                await uow.SaveAsyncWithTracking(ct);

                userDataFromUsers = [.. userDataFromUsers, .. queryDataFromUsers];

                logs.AppendLine($"Adding {queryDataFromUsers.Count} query builder recipients from group: {query.GroupName}");
            }
        }

        var adHocUserData = message.MessageLogEntries.SelectMany(entry => entry.Recipients.Where(x => x.AdHoc != null).Select(x => new MessageRecipientUserDataVM { FullName = x.AdHoc.Name, Email = x.AdHoc.Email, PhoneNumber = x.AdHoc.PhoneNumber }));

        if (!adHocUserData.IsNullOrEmpty())
        {
            logs.AppendLine($"Adding {adHocUserData.Count()} recipients from ad hoc source");
        }

        uow.Db.Insert(new MessageLogActivity
        {
            MessageLogId = message.Id,
            Title = "Preparation",
            Activity = logs.ToString(),
            CreatedByUser = message.CreatedByUser,
            CreatedByUserId = message.CreatedByUserId
        });

        List<MessageRecipientUserDataVM> data = [.. userDataFromUsers, .. userDataFromMessagingGroup, .. adHocUserData];

        return data.AsParallel();
    }

    private static readonly Func<List<MessageRecipient>, ParallelQuery<MessageRecipientUserDataVM>, IReadOnlyList<string>, string, IEnumerable<string>, string[], List<SinglePreparedMessageVM>> RecipientWithDataUnionQuery =
        (recipients, data, systemKeys, logo, systemTags, disallowedDomains) =>
        recipients.AsParallel().Select(x => data.FirstOrDefault(y => y.UserId == x.UserId && y.MessagingGroupId == x.MessagingGroupId).GetConsolidatedData(x, systemKeys, logo, systemTags, disallowedDomains))
        .Where(x => x != null).ToList();

    private static void PrepareTextMessage(PreparedMessageVM message, MessageLogEntry entry)
    {
        if (!entry.TextRequired)
            return;

        if (!string.IsNullOrEmpty(entry.TextTemplate))
            message.TextTemplate = entry.TextTemplate;
        else
        {
            if (!string.IsNullOrEmpty(entry.ContainerTextTemplate))
            {
                message.TextTemplate = entry.ContainerTextTemplate;
            }

            if (!string.IsNullOrEmpty(entry.BodyTextTemplate))
            {
                if (!string.IsNullOrEmpty(message.TextTemplate))
                    message.TextTemplate = message.TextTemplate.Replace("{body}", entry.BodyTextTemplate);
                else
                    message.TextTemplate = entry.BodyTextTemplate;
            }
        }

        return;
    }

    private async Task PrepareHtmlMessage(PreparedMessageVM message, MessageLogEntry entry, CancellationToken ct)
    {
        if (!entry.EmailRequired)
            return;

        if (!string.IsNullOrEmpty(entry.HtmlTemplate))
            message.HtmlTemplate = await httpClient.ReadPhysicalFileAsString(entry.HtmlTemplate, ct: ct);
        else
        {
            if (!string.IsNullOrEmpty(entry.ContainerHtmlTemplate))
            {
                message.HtmlTemplate = await httpClient.ReadPhysicalFileAsString(entry.ContainerHtmlTemplate, ct: ct);
            }

            if (!string.IsNullOrEmpty(entry.BodyHtmlTemplate))
            {
                var bodyTemplate = await httpClient.ReadPhysicalFileAsString(entry.BodyHtmlTemplate, ct: ct);
                if (!string.IsNullOrEmpty(message.HtmlTemplate))
                {
                    message.HtmlTemplate = message.HtmlTemplate.Replace("{body}", bodyTemplate);
                }
                else
                    message.HtmlTemplate = bodyTemplate;
            }
        }
    }

}