﻿@using LendQube.Entities.Core.Constants
@using LendQube.Entities.Core.Extensions
@using LendQube.Infrastructure.Core.Authentication
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using LendQube.Infrastructure.Core.Extensions
@using NodaTime
@inject IJSRuntime jSRuntime
@inherits AsyncComponentBase
@typeparam T where T : class

<div id="@ElementId">
    @if (TableDefinition?.DropDowns != null)
    {
        <div class="form-row">
            <select class="form-select __sm" @bind-value="dropDownFilter" @bind-value:event="oninput" @onchange="() => GetData()">
                <option label="Select"></option>
                @foreach (var item in TableDefinition.DropDowns)
                {
                    <option>@item</option>
                }
            </select>

        </div>
    }
    <div class="table-wrapper">
        <div class="search-wrapper flex">

            <div class="__filter">
                @if (TableDefinition is not { ColumnFilters: null or [] })
                {
                    <button class="btn btn__sm btn--link __icon btn__filter"
                            id="filterTarget_@ElementId" type="button" data-bs-toggle="modal"
                            data-bs-target="#filterModal_@ElementId">

                        @if (!filterApplied)
                        {
                            <span>
                                Filter
                            </span>

                        }
                        else
                        {
                            <span>
                                Filtered by:&nbsp
                            </span>
                            <span class="filter-values">
                                @string.Join(' ', pagedData.ComplexFilter.Where(x => x.IsValid).Select(x => x.Condition.HasValue ? $"{x.Condition} {x.ColumnDisplayName}" : x.ColumnDisplayName))
                            </span>
                        }
                    </button>
                }
            </div>

            @if (TableDefinition is { NoGeneralSearch: false })
            {
                <div class="search-grid">
                    <div class="search-item">
                        <input class="form-input search-input" type="text" placeholder="Search all records" @bind-value="textFilter" @bind-value:event="oninput" @onkeydown="@KeyboardSearch">
                        <div class="search-buttons">
                            <button class="btn btn__sm btn--icon" type="button" @onclick="() => GetData()">
                                <span class="svg svg-search"></span>
                            </button>
                            <button class="btn btn__sm btn--icon __danger" type="button" @onclick="() => ResetSearch()" hidden="@(string.IsNullOrEmpty(textFilter) && string.IsNullOrEmpty(pagedData.TextFilter))">
                                <span class="svg svg-clear"></span>
                            </button>
                        </div>
                    </div>
                </div>

            }


            @if (TableDefinition?.TopActionButtons != null)
            {
                foreach (var item in TableDefinition.TopActionButtons)
                {
                    if (item.ShowCondition != null && !item.ShowCondition())
                        continue;

                    if (!string.IsNullOrEmpty(item.ModalName))
                    {
                        @if (string.IsNullOrEmpty(item.Icon))
                        {
                            <button type="button" data-bs-toggle="modal" data-bs-target="#@item.ModalName" class="btn @item.ButtonClass btn__sm" @onclick="() => { if(item.Action != null) item.Action(); }">
                                @item.Name
                            </button>
                        }
                        else
                        {
                            <button type="button" data-bs-toggle="modal" data-bs-target="#@item.ModalName" class="btn @item.ButtonClass btn__sm __icon" @onclick="() => { if(item.Action != null) item.Action(); }">
                                <i data-feather="@item.Icon"></i> @item.Name
                            </button>
                        }
                    }
                    else if (item.Action != null)
                    {
                        @if (string.IsNullOrEmpty(item.Icon))
                        {
                            <button type="button" @onclick="() => item.Action()" class="btn @item.ButtonClass btn__sm">
                                @item.Name
                            </button>
                        }
                        else
                        {
                            <button type="button" @onclick="() => item.Action()" class="btn @item.ButtonClass btn__sm __icon">
                                <i data-feather="@item.Icon"></i> @item.Name
                            </button>
                        }
                    }
                    else if (item.Url != null)
                    {
                        @if (string.IsNullOrEmpty(item.Icon))
                        {
                            <a href="@item.Url" class="btn @item.ButtonClass btn__sm">
                                @item.Name
                            </a>
                        }
                        else
                        {
                            <a href="@item.Url" class="btn @item.ButtonClass btn__sm __icon">
                                <i data-feather="@item.Icon"></i> @item.Name
                            </a>
                        }
                    }
                }
            }
        </div>

        <div class="overflow-wrap @(Loading ? "sk-loading" : "")">
            <div class="sk-spinner sk-spinner-double-bounce">
                <div class="sk-double-bounce1"></div>
                <div class="sk-double-bounce2"></div>
            </div>
            <table class="table table-responsive-lg  table-borderless">
                <thead>
                    <tr>
                        @if (TableDefinition != null)
                        {
                            foreach (var item in TableDefinition.ColumnHeaders.Where(x => !x.IsHidden))
                            {
                                <th @onclick="() => OrderColumn(item)">
                                    @item.Title
                                    @if (data != null && data.OrderByColumn == item.Name)
                                    {
                                        <i class="pull-right fa @(data.OrderByDirection == DataOrderDirection.ASC ? "fa-sort-up" : "fa-sort-down")"></i>
                                    }
                                    else if (item.IsSortable || item.IsSortableVM)
                                    {
                                        <i class="pull-right fa fa-sort"></i>
                                    }
                                </th>
                            }

                            if (TableDefinition.HasActionSlot)
                            {
                                <th></th>
                            }
                        }
                    </tr>
                </thead>
                <tbody>
                    @if (data != null && TableDefinition != null)
                    {
                        if (data.Data.Count == 0)
                        {
                            var message = new System.Text.StringBuilder();
                            if (!pagedData.ComplexFilter.IsNullOrEmpty())
                            {
                                message.Append("applied filter ");
                            }

                            if (!string.IsNullOrEmpty(pagedData.DropDownFilter))
                            {
                                if (message.Length > 0)
                                    message.Append($"and ");
                                message.Append($"selected value: <strong  style='font-weight: 500'>{dropDownFilter}</strong> ");
                            }

                            if (!string.IsNullOrEmpty(pagedData.TextFilter))
                            {
                                if (message.Length > 0)
                                    message.Append($"and ");
                                message.Append($"search value: <strong  style='font-weight: 500'>{textFilter}</strong> ");

                            }

                            if (message.Length == 0)
                                message.Append("No data yet.");
                            else
                                message.Insert(0, "No result found for ");
                            <tr>
                                <td colspan="@TableDefinition.ColumnHeaders.Count">

                                    <div class="alert warning hasFlex">
                                        <div class="al-flex">
                                            <div class="al-content">
                                                <span class="al__title" role="alert" style="font-weight: 300">
                                                    @((MarkupString)message.ToString())
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                        else
                        {
                            var properties = typeof(T).GetProperties();
                            var columnHeaders = TableDefinition.ColumnHeaders.Where(x => properties.Select(y => y.Name).Contains(x.Name));

                            foreach (var row in data.Data)
                            {
                                var rowDeleteData = TableDefinition.HasDelete ? new Dictionary<string, object>() : null;
                                var rowInfoData = TableDefinition.HasInfo ? new Dictionary<string, object>() : null;
                                <tr class="table-row">
                                    @foreach (var item in columnHeaders)
                                    {
                                        var property = properties.FirstOrDefault(x => x.Name == item.Name);

                                        var value = property.GetValue(row);

                                        @if (value != null)
                                        {
                                            var type = value.GetType();

                                            if (type.IsBooleanType())
                                            {
                                                var boolValue = (bool)value;

                                                if (TableDefinition.HasDelete && item.DecoratorTypes.HasDecoratorType(TableDecoratorType.ShowInDelete))
                                                {
                                                    rowDeleteData.Add(item.Title, boolValue);
                                                }
                                                if (TableDefinition.HasInfo && item.DecoratorTypes.HasDecoratorType(TableDecoratorType.ShowInInfo))
                                                {
                                                    rowInfoData.Add(item.Title, boolValue);
                                                }


                                                if (item.IsHidden)
                                                    continue;

                                                if (CheckboxChanges.TryGetValue(item.Name, out var storedCheckboxAction))
                                                {
                                                    var storedCheckBox = storedCheckboxAction.FirstOrDefault(x => row.Equals(x.Row));
                                                    if (storedCheckBox != null)
                                                        boolValue = storedCheckBox.NewValue;
                                                }
                                                <td>
                                                    <div class="check-group">
                                                        <label class="check-label">

                                                            @if (item.DecoratorTypes.HasDecoratorType(TableDecoratorType.GroupActionCheckbox))
                                                            {
                                                                if (boolValue)
                                                                {
                                                                    <input class="check-input" type="checkbox" @onchange="(e) => RecordCheckboxAction(item.Name, new CheckboxAction((bool)value, (bool)e.Value, row))" checked="checked">
                                                                }
                                                                else
                                                                {
                                                                    <input class="check-input" type="checkbox" @onchange="(e) => RecordCheckboxAction(item.Name, new CheckboxAction((bool)value, (bool)e.Value, row))">
                                                                }

                                                            }
                                                            else
                                                            {
                                                                if (boolValue)
                                                                {
                                                                    <input class="check-input" type="checkbox" checked="checked" disabled>
                                                                }
                                                                else
                                                                {
                                                                    <input class="check-input" type="checkbox" disabled>
                                                                }
                                                            }

                                                            <span class="checkmark"></span>
                                                        </label>
                                                    </div>
                                                </td>
                                            }
                                            else
                                            {
                                                if (type.IsDecimalType())
                                                {
                                                    var decimalDecorator = (DbDecimalAttribute)Attribute.GetCustomAttribute(property, typeof(DbDecimalAttribute));
                                                    if (decimalDecorator != null)
                                                    {
                                                        value = decimal.Parse(value.ToString()).ToString($"n{decimalDecorator.Precision}");
                                                    }
                                                    else
                                                    {
                                                        value = decimal.Parse(value.ToString()).ToString($"n2");
                                                    }
                                                }
                                                else if (type.IsDoubleType())
                                                {
                                                    value = value.ToString();
                                                }
                                                else if (type.IsNumericType() && !type.IsEnum())
                                                {
                                                    value = long.Parse(value.ToString()).ToString($"n0");
                                                }
                                                else if (type.IsGenericListString())
                                                {
                                                    value = string.Join(", ", value as List<string>);
                                                }


                                                if (TableDefinition.HasDelete && item.DecoratorTypes.HasDecoratorType(TableDecoratorType.ShowInDelete))
                                                {
                                                    rowDeleteData[item.Title] = value;
                                                }
                                                if (TableDefinition.HasInfo && item.DecoratorTypes.HasDecoratorType(TableDecoratorType.ShowInInfo))
                                                {
                                                    rowInfoData[item.Title] = value;
                                                }

                                                if (item.IsHidden)
                                                    continue;

                                                <td>

                                                    @if (type.IsSupportedDateType())
                                                    {
                                                        <LocalTime Value="value" />
                                                    }
                                                    else if (type.IsEnum())
                                                    {
                                                        @value.ToString().SplitOnUpper()
                                                    }
                                                    else if (type.IsPhoneNumber())
                                                    {
                                                        @(((PhoneNumber)value).ToString())
                                                    }
                                                    else
                                                    {
                                                        @((MarkupString)(value as string))
                                                    }
                                                </td>
                                            }
                                        }
                                        else
                                        {

                                            if (TableDefinition.HasDelete && item.DecoratorTypes.HasDecoratorType(TableDecoratorType.ShowInDelete))
                                            {
                                                rowDeleteData[item.Title] = value;
                                            }
                                            if (TableDefinition.HasInfo && item.DecoratorTypes.HasDecoratorType(TableDecoratorType.ShowInInfo))
                                            {
                                                rowInfoData[item.Title] = value;
                                            }

                                            if (item.IsHidden)
                                                continue;

                                            <td>
                                            </td>
                                        }

                                    }

                                    @if (TableDefinition.HasActionSlot)
                                    {
                                        <td>
                                            <div class="icon-wrapper">
                                                @if (TableDefinition.HasInfo)
                                                {
                                                    <button class="btn btn__sm btn--icon __default" @onclick='() => ShowInfo(rowInfoData)'>
                                                        <i data-feather="eye"></i>
                                                    </button>
                                                }

                                                @if (!TableDefinition.RowActionButtons.IsNullOrEmpty())
                                                {
                                                    foreach (var item in TableDefinition.RowActionButtons)
                                                    {
                                                        if (item.ShowCondition != null && !item.ShowCondition(row))
                                                        {
                                                            continue;
                                                        }

                                                        if (string.IsNullOrEmpty(item.Icon))
                                                        {
                                                            if (item.Action == null)
                                                            {

                                                                <a class="btn btn__sm  @item.ButtonClass" href="@item.Url">
                                                                    @item.Name
                                                                </a>
                                                            }
                                                            else
                                                            {
                                                                <a class="btn btn__sm  @item.ButtonClass" @onclick="() => item.Action(row)">
                                                                    @item.Name
                                                                </a>
                                                            }
                                                        }
                                                        else
                                                        {
                                                            if (item.Action == null)
                                                            {
                                                                <a class="btn btn__sm btn--icon @item.IconClass" href="@item.Url" title="@item.Name" data-bs-toggle="tooltip">
                                                                    <i data-feather="@item.Icon"></i>
                                                                </a>

                                                            }
                                                            else
                                                            {
                                                                <a class="btn btn__sm btn--icon @item.IconClass" @onclick="() => item.Action(row)" title="@item.Name" data-bs-toggle="tooltip">
                                                                    <i data-feather="@item.Icon"></i>
                                                                </a>

                                                            }
                                                        }
                                                    }
                                                }

                                                @if (TableDefinition.HasEdit)
                                                {
                                                    <button class="btn btn__sm btn--icon __edit" @onclick='() => Edit(row.DeepCopy())'>
                                                        <i data-feather="edit-3"></i>
                                                    </button>
                                                }

                                                @if (TableDefinition.HasDelete)
                                                {
                                                    <button class="btn btn__sm btn--icon __danger" @onclick='() => Delete(row, rowDeleteData)'>
                                                        <i data-feather="trash-2"></i>
                                                    </button>
                                                }
                                            </div>
                                        </td>
                                    }
                                </tr>

                            }
                        }
                    }
                </tbody>
            </table>

        </div>

        <div class="table-footer flex __justify-between">
            <div class="records-item">
                <span class="records text_medium"> Show</span>
                <select class="form--select" @bind-value="pagedData.PageSize" @bind-value:event="oninput" @onchange="ChangePage">
                    <option>5</option>
                    <option>10</option>
                    <option>25</option>
                    <option>50</option>
                    <option>100</option>
                </select>
                <span class="records text_medium"> records. @((data?.From ?? 0).ToString("n0")) to @((data?.To ?? 0).ToString("n0")) of @((data?.ResultSetTotal ?? 0).ToString("n0"))</span>
            </div>
            @if (data != null)
            {
                <div class="pagination-item">
                    @if (data.HasPreviousPage)
                    {
                        <a @onclick="() => FirstPage()">
                            <span class="svg svg-double-chevron rotate-180"></span>
                        </a>
                        <a @onclick="() => PreviousPage()">
                            <span class="svg svg-chevron rotate-180"></span>
                        </a>
                    }
                    <span class="label">Page</span>
                    <input type="text" class="form--input" @bind-value="pagedData.Page" @bind-value:event="oninput" @onchange="ChangePage">
                    <span class="label">of @data.LastPage</span>
                    @if (data.HasNextPage)
                    {
                        <a @onclick="() => NextPage()">
                            <span class="svg svg-chevron"></span>
                        </a>
                        <a @onclick="() => LastPage()">
                            <span class="svg svg-double-chevron"></span>
                        </a>
                    }
                </div>
            }
        </div>
    </div>

    @if (TableDefinition is { HasDelete: true })
    {
        <div class="modal fade" id="@deleteModalName" tabindex="-1" aria-labelledby="@(deleteModalName)_Label"
             aria-modal="true" role="dialog">
            <div class="modal-dialog width-md">
                <div class="modal-content">
                    <div class="modal-header">
                        <div class="__title">
                            <h5 class="modal-title" id="@(deleteModalName)_Label">Remove Data</h5>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p> Are you sure you want to delete this: </p>

                        @if (dataToDelete != null)
                        {
                            foreach (var item in dataToShowForDelete)
                            {
                                <p>
                                    @if (item.Value != null && item.Value.GetType().IsBooleanType())
                                    {
                                        <div class="check-group">
                                            <label class="check-label">
                                                <strong>@item.Key </strong>
                                                @if ((bool)item.Value)
                                                {
                                                    <input class="check-input" type="checkbox" checked="checked" disabled>
                                                }
                                                else
                                                {
                                                    <input class="check-input" type="checkbox" disabled>
                                                }
                                                <span class="checkmark"></span>
                                            </label>
                                        </div>
                                    }
                                    else if (item.Value != null && item.Value.GetType().IsSupportedDateType())
                                    {
                                        <strong>@item.Key: </strong>
                                        <LocalTime Value="item.Value" />
                                    }
                                    else if (item.Value != null && (item.Value.GetType().IsEnum()))
                                    {
                                        <strong>@item.Key: </strong>
                                        @item.Value.ToString().SplitOnUpper()
                                    }
                                    else if (item.Value != null && item.Value.GetType().IsPhoneNumber())
                                    {
                                        <strong>@item.Key: </strong>
                                        @(((PhoneNumber)item.Value).ToString())
                                    }
                                    else
                                    {
                                        <strong>@item.Key: </strong>
                                        @((MarkupString)(item.Value as string))
                                    }
                                </p>
                            }

                        }

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn--default" data-bs-dismiss="modal">Close</button>
                        <LoadButton Label="Delete" OnClick="SaveDelete" Css="btn--danger" />
                    </div>
                </div>
            </div>
        </div>
    }

    @if (TableDefinition is { HasInfo: true })
    {
        <div class="modal fade" id="@infoModalName" tabindex="-1" aria-labelledby="@(infoModalName)_Label"
             aria-modal="true" role="dialog">
            <div class="modal-dialog width-md">
                <div class="modal-content">
                    <div class="modal-header">
                        <div class="__title">
                            <h5 class="modal-title" id="@(infoModalName)_Label">More Info</h5>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">

                        @if (dataToShowForInfo != null)
                        {
                            foreach (var item in dataToShowForInfo)
                            {
                                <p>
                                    @if (item.Value != null && item.Value.GetType().IsBooleanType())
                                    {
                                        <div class="check-group">
                                            <label class="check-label">
                                                <strong>@item.Key </strong>
                                                @if ((bool)item.Value)
                                                {
                                                    <input class="check-input" type="checkbox" checked="checked" disabled>
                                                }
                                                else
                                                {
                                                    <input class="check-input" type="checkbox" disabled>
                                                }
                                                <span class="checkmark"></span>
                                            </label>
                                        </div>
                                    }
                                    else if (item.Value != null && item.Value.GetType().IsSupportedDateType())
                                    {
                                        <strong>@item.Key: </strong>
                                        <LocalTime Value="item.Value" />
                                    }
                                    else if (item.Value != null && (item.Value.GetType().IsEnum()))
                                    {
                                        <strong>@item.Key: </strong>
                                        @item.Value.ToString().SplitOnUpper()
                                    }
                                    else if (item.Value != null && item.Value.GetType().IsPhoneNumber())
                                    {
                                        <strong>@item.Key: </strong>
                                        @(((PhoneNumber)item.Value).ToString())
                                    }
                                    else
                                    {
                                        <strong>@item.Key: </strong>
                                        @((MarkupString)(item.Value as string))
                                    }
                                </p>
                            }

                        }

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn--default" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    }

    @if (TableDefinition is not { ColumnFilters: null or [] })
    {
        <div class="modal fade" id="filterModal_@ElementId" tabindex="-1" aria-labelledby="filterModalLabel_@ElementId"
             aria-modal="true" role="dialog">
            <div class="modal-dialog width-xlg filter-controls">
                <div class="modal-content">
                    <div class="modal-header">
                        <div class="__title">
                            <h5 class="modal-title" id="filterModalLabel_@ElementId">Filter</h5>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form class="filter-items">
                            <div class="filter-inputs">
                                @foreach (var item in complexFilters)
                                {
                                    <DataTableFilterItem @bind-FilterValue="@complexFilters" FilterKey="item.Key" RemoveFilter="() => DeleteFilter(item.Key)" Filters="TableDefinition.ColumnFilters" JSRuntime="jSRuntime"
                                                         CancellationToken="Cancel" @key="@item.Key"/>
                                }

                                <div class="add-row">
                                    <button class="btn btn__sm btn--gray __icon" type="button" @onclick="() => AddFilter()">
                                        <span class="svg svg-add"></span>Add filter
                                    </button>
                                </div>
                            </div>

                            <div class="form-row __footer">
                                <button class="btn btn--default" type="button" @onclick="() => ClearFilter()">Reset</button>
                                <button class="btn btn--primary" data-bs-dismiss="modal" type="button" @onclick="() => GetData()" disabled="@(!complexFilters?.Values?.Any(x => x.IsValid))">
                                    Apply
                                    Filter
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
@code
{
    [Parameter] public ColumnList TableDefinition { get; set; }
    [Parameter, EditorRequired] public Func<DataFilterAndPage, CancellationToken, ValueTask<TypedBasePageList<T>>> LoadData { get; set; }
    [Parameter] public Func<T, CancellationToken, ValueTask> EditRow { get; set; }
    [Parameter] public Func<T, Func<Task>, CancellationToken, ValueTask<bool>> DeleteRow { get; set; }
    [Parameter] public string ElementId { get; set; } = SecurityDriven.FastGuid.NewGuid().ToString();
    [Parameter] public bool DeferLoading { get; set; } = false;
    [Parameter] public Action<int> CheckboxSelectionEvent { get; set; }
    [Parameter] public int DefaultPageSize { get; set; } = 10;

    public Dictionary<string, List<CheckboxAction>> CheckboxChanges { get; init; } = [];

    private DataFilterAndPage pagedData = new DataFilterAndPage { PageSize = 10, Page = 1 };
    private TypedBasePageList<T> data;
    private Dictionary<Guid, ComplexFilter> complexFilters = [];
    private string deleteModalName, infoModalName;
    private T dataToDelete;
    private Dictionary<string, object> dataToShowForDelete, dataToShowForInfo;
    private string textFilter, dropDownFilter;
    private bool filterApplied = false;


    #region Load

    public bool Loading { get; set; } = false;
    private bool loaded = false;

    public void SetTableDefinition(ColumnList definition) => TableDefinition = definition;

    public async Task LoadElement(bool reload = true)
    {
        if (loaded && !reload)
            return;

        loaded = true;

        textFilter = dropDownFilter = null;
        CheckboxChanges.Clear();
        CheckboxSelectionEvent?.Invoke(0);
        pagedData = new DataFilterAndPage { PageSize = DefaultPageSize, Page = 1 };
        if (TableDefinition != null && !TableDefinition.ColumnFilters.IsNullOrEmpty())
        {
            complexFilters.Clear();
            complexFilters[SecurityDriven.FastGuid.NewGuid()] = new ComplexFilter();
        }
        await GetData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            deleteModalName = $"deleteModal_{ElementId}";
            infoModalName = $"infoModal_{ElementId}";
            if (!DeferLoading)
                await LoadElement();
        }
        await jSRuntime.RunFeather(Cancel);
    }

    private async Task GetData()
    {
        if (Loading)
            return;

        Loading = true;
        pagedData.TextFilter = textFilter;
        pagedData.DropDownFilter = dropDownFilter;
        pagedData.ComplexFilter = complexFilters?.Values.Where(x => x.IsValid).ToList();

        StateHasChanged();
        data = null;
        data = await LoadData(pagedData, Cancel);

        if(data != null)
        {
            pagedData.Page = data.CurrentPage;

            if (!pagedData.ComplexFilter.IsNullOrEmpty())
                filterApplied = true;
        }
        else
        {
            pagedData.Page = 1;
        }

        Loading = false;
        StateHasChanged();
    }

    public async Task Refresh()
    {
        pagedData.Page = 1;

        await GetData();
    }


    private async Task ChangePage()
    {
        if (data != null)
        {
            if (pagedData.Page <= data.LastPage && pagedData.Page > 0)
            {
                await GetData();
            }
            else
            {
                if (pagedData.Page != data.CurrentPage)
                    pagedData.Page = data.CurrentPage;
            }
        }
    }

    private async Task OrderColumn(ColumnHeader column)
    {
        if (data == null || data.Data.IsNullOrEmpty() || (!column.IsSortable && !column.IsSortableVM))
            return;
        
        pagedData.OrderByColumn = pagedData.OrderByColumnVM = null;

        if (column.IsSortable)
            pagedData.OrderByColumn = column.Name;
        else if (column.IsSortableVM)
            pagedData.OrderByColumnVM = column.Name;

        pagedData.OrderByDirection = data.OrderByDirection == DataOrderDirection.ASC ? DataOrderDirection.DESC : DataOrderDirection.ASC;
        await GetData();
    }

    #endregion

    #region filterAndPage

    private async Task NextPage()
    {
        if (data != null && data.HasNextPage)
        {
            pagedData.Page += 1;
            await GetData();
        }
    }

    private async Task PreviousPage()
    {
        if (data != null && data.HasPreviousPage)
        {
            pagedData.Page -= 1;
            await GetData();
        }
    }

    private async Task FirstPage()
    {
        if (data != null && data.HasPreviousPage)
        {
            pagedData.Page = 1;
            await GetData();
        }
    }

    private async Task LastPage()
    {
        if (data != null && data.HasNextPage)
        {
            pagedData.Page = data.LastPage;
            await GetData();
        }
    }
    #endregion

    #region Search
    private async Task KeyboardSearch(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && (!string.IsNullOrEmpty(textFilter) || !string.IsNullOrEmpty(pagedData?.TextFilter)))
        {
            await GetData();
        }
    }

    private async Task ResetFilter()
    {
        pagedData.AllFilter = null;
        await Refresh();
    }

    private async Task ResetSearch()
    {
        textFilter = null;
        await Refresh();
    }
    #endregion

    #region ManageData
    private async Task Edit(T data)
    {
        await EditRow(data, Cancel);
    }

    private async Task Delete(T data, Dictionary<string, object> dataToShow)
    {
        dataToDelete = data;
        dataToShowForDelete = dataToShow;
        StateHasChanged();

        await jSRuntime.OpenModal(deleteModalName, Cancel);
    }


    private async Task SaveDelete()
    {
        _ = await DeleteRow(dataToDelete, async () =>
        {
            await jSRuntime.CloseModal(deleteModalName, Cancel);
            await Refresh();

        }, Cancel);
    }

    private async Task ShowInfo(Dictionary<string, object> dataToShow)
    {
        dataToShowForInfo = dataToShow;
        StateHasChanged();
        await jSRuntime.OpenModal(infoModalName, Cancel);
    }
    #endregion

    #region CheckboxActions

    private void RecordCheckboxAction(string columnName, CheckboxAction action)
    {
        if (CheckboxChanges.TryGetValue(columnName, out var actions))
        {
            if (action.OriginalValue == action.NewValue)
            {
                var removed = actions.RemoveAll(x => x.Row == action.Row);
                if (removed > 0)
                {
                    if (actions.IsNullOrEmpty())
                        CheckboxChanges.Remove(columnName);
                    else
                        CheckboxChanges[columnName] = actions;
                }
            }
            else
            {
                actions.Add(action);
                CheckboxChanges[columnName] = actions;
            }
        }
        else
        {
            if (action.OriginalValue != action.NewValue)
            {
                CheckboxChanges[columnName] = [action];
            }
        }

        CheckboxSelectionEvent?.Invoke(CheckboxChanges.Sum(x => x.Value.Count));
    }

    #endregion

    #region Filters

    private void AddFilter()
    {
        complexFilters.Add(SecurityDriven.FastGuid.NewGuid(), new ComplexFilter { ShouldHaveCondition = true });
        StateHasChanged();
    }

    private async Task ClearFilter()
    {
        complexFilters.Clear();
        complexFilters[SecurityDriven.FastGuid.NewGuid()] = new ComplexFilter();
        if (filterApplied)
        {
            await GetData();
            filterApplied = false;
        }
        else
            StateHasChanged();
    }

    private void DeleteFilter(Guid id)
    {
        complexFilters.Remove(id);
        StateHasChanged();
    }

    #endregion
} 