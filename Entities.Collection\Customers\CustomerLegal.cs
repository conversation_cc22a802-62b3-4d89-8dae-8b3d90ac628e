﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using NodaTime;

namespace LendQube.Entities.Collection.Customers;
public class CustomerLegal : BaseEntityWithIdentityId<CustomerLegal>
{
    [DbGuid]
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public string SIDNumber { get; set; }
    public string CaseReference { get; set; }
    public decimal ClaimAmount { get; set; }
    public string Court { get; set; }
    public string Country { get; set; }
    public DateOnly? ScotClaimSubmitDate { get; set; }
    public DateOnly? LDORDate { get; set; }
    public DateOnly? DecreeGrantedDate { get; set; }
    public DateOnly? MCOLFileDate { get; set; }
    public DateOnly? MCOLCCJAppliedFor { get; set; }
    public DateOnly? MCOLCCJGranted { get; set; }
    public DateOnly? TTPDate { get; set; }
    public decimal TPPAmount { get; set; }
    public DateOnly? EACourtFeePaidDate { get; set; }
    public DateOnly? EAServeDate { get;set; }
    public DateOnly? SuspendedOrderGrantedDate { get; set; }
    public DateOnly? SuspendedOrderActionDate { get; set; }
    public decimal SuspendedOrderAmount { get; set; }
    public DateOnly? ClaimIssueDate { get; set; }
    public DateOnly? NotSuitableForMCOL { get; set; }
    public string NotSuitableReason { get; set; }
    public DateOnly? NoaDate { get; set; }
    public string Source { get; set; }
}
