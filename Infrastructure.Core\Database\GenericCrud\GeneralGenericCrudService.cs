﻿using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;

namespace LendQube.Infrastructure.Core.Database.GenericCrud;

public class GeneralGenericCrudService(IUnitofWork uow)
{
    public IUnitofWork Uow => uow;
    public IRelationalDbRepository Db => Uow.Db;

    public ValueTask<TypedBasePageList<T>> GetTypeBasedPagedData<T>(ISpecification<T> spec, DataFilterAndPage filterAndPage, CancellationToken ct) where T : class, IBaseEntityForRelationalDb
    {
        spec.DoAllFilter(filterAndPage);
        var dbSet = uow.Db.NotTrackedWithSpec(spec);

        var paginator = new PagedList<T>(dbSet, filterAndPage);

        var tokenSource = CancellationTokenSource.CreateLinkedTokenSource(ct);
        var token = tokenSource.Token;

        return Db.ConcurrencyManager.Execute(() => paginator.ToType(token), tokenSource, token);
    }

    public ValueTask<TypedBasePageList<T>> GetTypeBasedPagedData<T>(IQueryable<T> dbSet, ISpecification<T> spec, DataFilterAndPage filterAndPage, CancellationToken ct)
    where T : class
    {
        spec.DoAllFilter(filterAndPage);

        var paginator = new PagedList<T>(dbSet, filterAndPage);

        return Db.ConcurrencyManager.Execute(() => paginator.ToType(ct), ct);
    }

    public ColumnList GetTableDefinition<T>(TableSettings<T> settings = null) where T : class =>
        GenericColumnAndFilterService<T, T>.GetTableDefinition(settings ?? new());

}
