﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Helpers;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.ViewModels.Messaging;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    private const string PreviewMessageModal = "PreviewMessageModal";
    private const string SendMessageModal = "SendMessageModal";
    private const string CustomMessagePreviewModal = "CustomMessagePreviewModal";

    private DataTable<CustomerMessageEntryVM> messagesTable;
    private ColumnList customerMessagesTableDefinition;
    private readonly PreviewMessageVM previewMessageModel = new();
    private readonly CreateCustomMessageVM SendMessageModel = new();
    private PreviewMessageVM customMessagePreviewModel = new();

    private readonly IReadOnlyList<string> systemKeys = [.. EnumExtensions.GetEnumNames<MessageTemplateSystemTags>()];
    private readonly IReadOnlyList<MessageChannel> availableCustomMessageChannels = [MessageChannel.Sms, MessageChannel.Email, MessageChannel.PushNotification];

    private List<MessagingTemplate> ConfiguredContainerTemplates { get; set; } = [];
    private IReadOnlyList<CustomerContactDetail> CustomerContacts { get; set; } = [];
    private List<CustomMessageContactVM> EmailContactOptions { get; set; } = [];
    private List<CustomMessageContactVM> PhoneContactOptions { get; set; } = [];
    private bool HasTemplateSelectionPermission => HasClaim(ManageCustomersNavigation.CustomerProfileSendMessageWithSelectedContainerPermission);

    private bool IsSmsSelected => SendMessageModel.ChannelsList != null && SendMessageModel.ChannelsList.Contains(MessageChannel.Sms);
    private bool IsPushSelected => SendMessageModel.ChannelsList != null && SendMessageModel.ChannelsList.Contains(MessageChannel.PushNotification);
    private bool IsEmailSelected => SendMessageModel.ChannelsList != null && SendMessageModel.ChannelsList.Contains(MessageChannel.Email);

    private void OnChannelSelectionChanged(object args)
    {
        InvokeAsync(StateHasChanged);
    }

    private void SetupMessageTable()
    {
        customerMessagesTableDefinition = CrudService.GetTableDefinition<MessageLogEntry, CustomerMessageEntryVM>();

        customerMessagesTableDefinition.RowActionButtons.Add(new RowActionButton("View", Icon: "eye", Action: async (object row) =>
        {
            await PreviewMessage(row as CustomerMessageEntryVM);
        }));

        customerMessagesTableDefinition.TopActionButtons.Add(new TopActionButton("Send Custom Message", Icon: "send", ModalName: SendMessageModal, Action: LoadMessageData));

        messagesTable.SetTableDefinition(customerMessagesTableDefinition);
    }

    private ValueTask<TypedBasePageList<CustomerMessageEntryVM>> LoadCustomerMessages(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<MessageLogEntry>
        {
            PrimaryCriteria = x => x.Recipients.Any(y => y.UserId == Data.Id || y.AdHoc.Key == Data.Id),
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
                EF.Functions.ILike(x.Name, filterAndPage.TextFilter)
                || EF.Functions.ILike(x.Subject, filterAndPage.TextFilter)
                || EF.Functions.ILike(x.Config.Description, filterAndPage.TextFilter)
                || x.Recipients.Any(y => y.UserId == Data.Id && y.TemplateValues.Any(z => EF.Functions.ILike(z.Value, filterAndPage.TextFilter))));
        }

        var mapping = CustomerMessageEntryVM.Mapping(Data.Id);
        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, mapping, ct: ct);
    }

    private async Task PreviewMessage(CustomerMessageEntryVM item)
    {
        previewMessageModel.Name = item.Name;
        previewMessageModel.Subject = item.Subject;
        previewMessageModel.TextRequired = item.HasText;
        previewMessageModel.EmailRequired = item.HasHtml;

        await BuildMessageTemplate(previewMessageModel, item);
        ReplaceTemplateKeys(previewMessageModel, item);

        await JSRuntime.OpenModal(PreviewMessageModal, Cancel);
        StateHasChanged();
    }

    private async Task PreviewCustomMessage()
    {
        var containerTemplate = SendMessageModel.ContainerTemplate ?? ConfiguredContainerTemplates.FirstOrDefault();

        var previewModel = new PreviewMessageVM
        {
            Name = Data.FullName,
            Subject = !string.IsNullOrEmpty(SendMessageModel.Subject) ? SendMessageModel.Subject : $"Message to {Data.FirstName}",
            TextRequired = IsSmsSelected || IsPushSelected,
            EmailRequired = IsEmailSelected,
            TemplateValues = [new(MessageTemplateSystemTags.Body.ToString().ToLower(), GetCustomMessageBody())]
        };

        await BuildCustomMessageTemplate(previewModel, containerTemplate);
        ReplaceTemplateKeys(previewModel, null);

        customMessagePreviewModel = previewModel;
        await JSRuntime.OpenModal(CustomMessagePreviewModal, Cancel);
        StateHasChanged();
    }

    private async Task BuildMessageTemplate(PreviewMessageVM model, CustomerMessageEntryVM item)
    {
        if (model.TextRequired)
        {
            model.TextTemplate = !string.IsNullOrEmpty(item.TextTemplate)
                ? item.TextTemplate
                : BuildTextTemplate(item.ContainerTextTemplate, item.BodyTextTemplate);
        }

        if (model.EmailRequired)
        {
            model.HtmlTemplate = !string.IsNullOrEmpty(item.HtmlTemplate)
                ? await HttpClient.ReadPhysicalFileAsString(item.HtmlTemplate, ct: Cancel)
                : await BuildHtmlTemplate(item.ContainerHtmlTemplate, item.BodyHtmlTemplate);
        }
    }

    private async Task BuildCustomMessageTemplate(PreviewMessageVM model, MessagingTemplate containerTemplate)
    {
        if (IsSmsSelected || IsPushSelected)
        {
            model.TextTemplate = containerTemplate?.TextTemplate != null
                ? containerTemplate.TextTemplate.Replace($"{{{MessageTemplateSystemTags.Body}}}", SendMessageModel.Text ?? "", StringComparison.OrdinalIgnoreCase)
                : SendMessageModel.Text ?? "";
        }

        if (IsEmailSelected)
        {
            if (containerTemplate?.HtmlTemplate != null)
            {
                var htmlTemplateContent = await HttpClient.ReadPhysicalFileAsString(containerTemplate.HtmlTemplate, ct: Cancel);
                model.HtmlTemplate = htmlTemplateContent.Replace($"{{{MessageTemplateSystemTags.Body}}}", SendMessageModel.Html ?? "", StringComparison.OrdinalIgnoreCase);
            }
            else
            {
                model.HtmlTemplate = SendMessageModel.Html ?? "";
            }
        }
    }

    private string BuildTextTemplate(string containerTemplate, string bodyTemplate)
    {
        var template = containerTemplate ?? "";
        if (!string.IsNullOrEmpty(bodyTemplate))
        {
            template = !string.IsNullOrEmpty(template)
                ? template.Replace($"{{{MessageTemplateSystemTags.Body}}}", bodyTemplate, StringComparison.OrdinalIgnoreCase)
                : bodyTemplate;
        }
        return template;
    }

    private async Task<string> BuildHtmlTemplate(string containerTemplate, string bodyTemplate)
    {
        var template = !string.IsNullOrEmpty(containerTemplate)
            ? await HttpClient.ReadPhysicalFileAsString(containerTemplate, ct: Cancel)
            : "";

        if (!string.IsNullOrEmpty(bodyTemplate))
        {
            var bodyContent = await HttpClient.ReadPhysicalFileAsString(bodyTemplate, ct: Cancel);
            template = !string.IsNullOrEmpty(template)
                ? template.Replace($"{{{MessageTemplateSystemTags.Body}}}", bodyContent, StringComparison.OrdinalIgnoreCase)
                : bodyContent;
        }

        return template;
    }

    private void ReplaceTemplateKeys(PreviewMessageVM model, CustomerMessageEntryVM item)
    {
        var keys = item?.Keys?.Union(systemKeys).Distinct() ?? model.TemplateValues?.Select(x => x.Key) ?? [];

        foreach (var key in keys)
        {
            var keyValue = GetKeyValue(key, item);
            ReplaceKeyInTemplate(model, key, keyValue);
        }
    }

    private string GetKeyValue(string key, CustomerMessageEntryVM item)
    {
        if (item?.TemplateValues != null)
        {
            var templateValue = item.TemplateValues.FirstOrDefault(x => x.Key.Equals(key, StringComparison.OrdinalIgnoreCase));
            if (templateValue != null)
                return templateValue.Value;
        }

        return systemKeys.Contains(key) switch
        {
            true when key.Equals(MessageTemplateSystemTags.FirstName.ToString(), StringComparison.OrdinalIgnoreCase) => Data.FirstName,
            true when key.Equals(MessageTemplateSystemTags.LastName.ToString(), StringComparison.OrdinalIgnoreCase) => Data.LastName,
            true when key.Equals(MessageTemplateSystemTags.Name.ToString(), StringComparison.OrdinalIgnoreCase) => Data.FullName,
            true when key.Equals(MessageTemplateSystemTags.Logo.ToString(), StringComparison.OrdinalIgnoreCase) => Config.Url.Logo,
            _ => ""
        };
    }

    private void ReplaceKeyInTemplate(PreviewMessageVM model, string key, string value)
    {
        var keyPattern = $"{{{key}}}";

        if (model.TextRequired && !string.IsNullOrEmpty(model.TextTemplate))
            model.TextTemplate = model.TextTemplate.Replace(keyPattern, value, StringComparison.OrdinalIgnoreCase);

        if (model.EmailRequired && !string.IsNullOrEmpty(model.HtmlTemplate))
            model.HtmlTemplate = model.HtmlTemplate.Replace(keyPattern, value, StringComparison.OrdinalIgnoreCase);

        if (!string.IsNullOrEmpty(model.Subject))
            model.Subject = model.Subject.Replace(keyPattern, value, StringComparison.OrdinalIgnoreCase);
    }

    private string GetCustomMessageBody() => IsEmailSelected ? SendMessageModel.Html ?? "" : SendMessageModel.Text ?? "";

    private async Task LoadMessageData()
    {
        ModalMessage.Close();

        if (ConfiguredContainerTemplates.IsNullOrEmpty())
        {
            ConfiguredContainerTemplates = await uow.Db.ManyAsync(Query<MessagingTemplate>
                .Where(x => !x.DisabledOn.HasValue && x.IsContainer), Cancel);
        }

        CustomerContacts = await uow.Db.ManyAsync(Query<CustomerContactDetail>.Where(x => x.ProfileId == Data.Id).OrderBy(x => x.OrderByDescending(y => y.Preferred)), Cancel); //ensure contact data is always refreshed

        EmailContactOptions.Clear();
        PhoneContactOptions.Clear();

        EmailContactOptions.AddRange(CustomerContacts.Where(x => !string.IsNullOrEmpty(x.Email)).Select(c => new CustomMessageContactVM { EmailAddress = c.Email, DisplayType = c.Preferred ? "Preferred Email" : "Email" }));
        PhoneContactOptions.AddRange(CustomerContacts.Where(x => x.PhoneNumber != null && x.PhoneNumber.IsValid()).Select(c => new CustomMessageContactVM { PhoneNumber = c.PhoneNumber, DisplayType = c.Preferred ? "Preferred Phone" : "Phone" }));


        StateHasChanged();
    }

    private void OnSelectContainerTemplate(long? value)
    {
        if (!value.HasValue)
        {
            SendMessageModel.ContainerTemplateId = null;
            SendMessageModel.ContainerTemplate = null;
            return;
        }

        SendMessageModel.ContainerTemplateId = value;
        SendMessageModel.ContainerTemplate = ConfiguredContainerTemplates.FirstOrDefault(x => x.Id == SendMessageModel.ContainerTemplateId);
    }

    private async Task SendCustomMessage()
    {
        ModalMessage.Close();

        if (!ValidateCustomMessage())
            return;
        if (string.IsNullOrEmpty(SendMessageModel.Subject))
        {
            SendMessageModel.Subject = $"Message to {Data.FirstName}";
        }

        SendMessageModel.Channels = SendMessageModel.ChannelsList.CombineFlags();
        var body = GetCustomMessageBody();

        SendMessageModel.ContainerTemplate ??= ConfiguredContainerTemplates.FirstOrDefault();

        var customMessageVm = new CustomMessageVM
        {
            Subject = SendMessageModel.Subject,
            Body = body,
            Channels = SendMessageModel.Channels,
            ContainerTemplate = SendMessageModel.ContainerTemplate,
            SenderEmail = SendMessageModel.SenderEmail
        };

        if (!string.IsNullOrEmpty(SendMessageModel.EmailAddress) || SendMessageModel.PreferredPhoneNumber?.IsValid() == true)
        {
            var adHocRecipient = new AdHocRecipient
            {
                Key = Data.Id,
                Name = Data.FullName,
                Email = SendMessageModel.EmailAddress,
                PhoneNumber = SendMessageModel.PreferredPhoneNumber
            };

            MessageBuilder.CustomMessage("Send Custom Message", UserName)
                .WithCustomRecipient(adHocRecipient, customMessageVm)
                .Queue(Queue);
        }
        else
        {
            MessageBuilder.CustomMessage("Send Custom Message", UserName)
                .WithCustomRecipient(Data.Id, customMessageVm)
                .Queue(Queue);
        }


        await JSRuntime.CloseModal(SendMessageModal, Cancel);
        TableMessage.Set(true, "Message sent successfully");
    }

    private bool ValidateCustomMessage() => ValidateCondition(!SendMessageModel.ChannelsList.Any(x => availableCustomMessageChannels.Contains(x)),
                   "Please select at least one channel to send the message")
            && ValidateCondition(IsEmailSelected && string.IsNullOrEmpty(SendMessageModel.Subject),
                   "Please provide a subject for the email")
            && ValidateCondition((IsSmsSelected || IsPushSelected) && string.IsNullOrEmpty(SendMessageModel.Text),
                   "Please provide a text for the message")
            && ValidateCondition(IsEmailSelected && string.IsNullOrEmpty(SendMessageModel.Html),
                   "Please provide a body for the email");

    private bool ValidateCondition(bool isError, string message)
    {
        if (isError)
        {
            ModalMessage.Warning(message);
            return false;
        }
        return true;
    }
}
