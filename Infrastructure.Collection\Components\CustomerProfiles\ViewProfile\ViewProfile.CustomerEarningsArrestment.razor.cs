﻿using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Specification;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    private DataTable<CustomerEarningsArrestmentVM> customerEarningsArrestmentsTable;
    private ColumnList customerEarningsArrestmentTableDefinition;
    private string AddCustomerEarningsArrestmentModal => "AddCustomerEarningsArrestmentModal";
     private string EditCustomerEarningsArrestmentModal => "EditCustomerEarningsArrestmentModal";

    private CustomerEarningsArrestment AddCustomerEarningsArrestmentModel { get; set; } = new();
    private CustomerEarningsArrestment EditCustomerEarningsArrestmentModel { get; set; } = new();

    private void SetupCustomerEarningsArrestment()
    {
        customerEarningsArrestmentTableDefinition = CrudService.GetTableDefinition<CustomerEarningsArrestment, CustomerEarningsArrestmentVM>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerEarningsArrestmentPermission),
            HasEdit = HasClaim(ManageCustomersNavigation.CustomerProfileViewEditCustomerEarningsArrestmentPermission)
        });

        customerEarningsArrestmentTableDefinition.TopActionButtons.Add(new TopActionButton("Add", ModalName: AddCustomerEarningsArrestmentModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddCustomerEarningsArrestmentPermission)));

        customerEarningsArrestmentsTable.SetTableDefinition(customerEarningsArrestmentTableDefinition);
    }

    private ValueTask<TypedBasePageList<CustomerEarningsArrestmentVM>> LoadCustomerEarningsArrestment(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerEarningsArrestment>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, CustomerEarningsArrestmentVM.Mapping, ct);
    }

    private ValueTask SubmitNewCustomerEarningsArrestment() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddCustomerEarningsArrestmentPermission, AddCustomerEarningsArrestmentModal, async () =>
    {
        AddCustomerEarningsArrestmentModel.ProfileId = Data.Id;
        uow.Db.Insert(AddCustomerEarningsArrestmentModel);
        await uow.SaveAsync(Cancel);

        return true;
    }, () =>
    {
        AddCustomerEarningsArrestmentModel = new();
        StateHasChanged();
        return customerEarningsArrestmentsTable.Refresh();
    });

    private ValueTask StartEditCustomerEarningsArrestment(CustomerEarningsArrestmentVM data, CancellationToken ct) => BaseEdit(ManageCustomersNavigation.CustomerProfileViewEditCustomerEarningsArrestmentPermission, EditCustomerEarningsArrestmentModal, () =>
    {
        EditCustomerEarningsArrestmentModel = data.Get(Data.Id);
        return Task.CompletedTask;
    }, ct);

    private ValueTask SubmitEditCustomerEarningsArrestment() => BaseSaveEdit(null, EditCustomerEarningsArrestmentModal, async () =>
    {
        uow.Db.Update(EditCustomerEarningsArrestmentModel);
        await uow.SaveAsync(Cancel);
        return true;
    }, customerEarningsArrestmentsTable.Refresh);


    private ValueTask<bool> DeleteCustomerEarningsArrestment(CustomerEarningsArrestmentVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerEarningsArrestmentPermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerEarningsArrestment>(x => x.Id == data.Id, ct);
        return result > 0;
    }, refresh);

}
