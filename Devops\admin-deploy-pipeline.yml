trigger: none
  
pool:
  name: Silicon Pipelines
  demands:
   - agent.name -equals SiliconLinuxAgent

variables:
  buildConfiguration: 'Release'
  dotnetVersion: '9.0.x'
  
stages:
- stage: Build
  displayName: 'Build Admin Application'
  jobs:
  - job: BuildJob
    displayName: 'Build Admin Project'
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET $(dotnetVersion)'
      inputs:
        packageType: sdk
        version: $(dotnetVersion)

    - task: DotNetCoreCLI@2
      displayName: 'Build And Publish Admin Website'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: '**/Web.Admin.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/Web.Admin'
        zipAfterPublish: true

    - task: PublishBuildArtifacts@1
      displayName: 'Save Packaged Application'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
        
- stage: Deploy
  displayName: 'Deploy to Production Server'
  dependsOn: Build
  condition: succeeded()
  jobs:
  - deployment: DeployToProduction
    displayName: 'Deploy to Production Server'
    environment: 'Production'
    strategy:
      runOnce:
        deploy:
          steps:          
          - task: DownloadBuildArtifacts@1
            displayName: 'Download Packaged Application'
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'drop'
              downloadPath: '$(System.ArtifactsDirectory)'

          - task: SSH@0
            displayName: 'Verify .NET Version'
            inputs:
              sshEndpoint: 'LendQube-Production-Server'
              readyTimeout: '60000'
              runOptions: 'inline'
              inline: |
                echo "Checking .NET version on server..."
                if ! dotnet --version | grep -q "^9\."; then
                  echo "ERROR: .NET 9.0 is not installed on the server"
                  echo "Current version: $(dotnet --version)"
                  exit 1
                else
                  echo ".NET version is compatible: $(dotnet --version)"
                fi

          - task: SSH@0
            displayName: 'Deploy Admin Website to Server'
            inputs:
              sshEndpoint: 'LendQube-Production-Server'
              runOptions: 'inline'
              inline: |
                sudo systemctl stop lendqube-admin || echo "Service not running, continuing..."
                
                if [ -f "/var/www/html/admin/AppSettings/appsettings.Production.json" ]; then
                  echo "Backing up appsettings.Production.json..."
                  cp /var/www/html/admin/AppSettings/appsettings.Production.json /tmp/appsettings.Production.json.bak
                else
                  echo "appsettings.Production.json not found, will create from Development settings..."
                fi
                
                if [ -d "/var/www/html/admin" ]; then
                  sudo cp -r /var/www/html/admin /var/www/html/admin_backup_$(date +%Y%m%d_%H%M%S)
                fi
                
                rm -rf /var/www/html/admin/*
                
                unzip -o $(System.ArtifactsDirectory)/drop/Web.Admin/*.zip -d /var/www/html/admin
                
                if [ -f "/tmp/appsettings.Production.json.bak" ]; then
                  echo "Restoring appsettings.Production.json..."
                  mv /tmp/appsettings.Production.json.bak /var/www/html/admin/AppSettings/appsettings.Production.json
                else
                  echo "Creating appsettings.Production.json from Development settings..."
                  cp /var/www/html/admin/AppSettings/appsettings.Development.json /var/www/html/admin/AppSettings/appsettings.Production.json
                fi
                
                chown -R silicon:silicon /var/www/html/admin
                
                sudo systemctl start lendqube-admin
                
                sleep 5
                if systemctl is-active --quiet lendqube-admin; then
                  echo "Admin website started successfully"
                else
                  echo "ERROR: Admin website failed to start"
                  journalctl -u lendqube-admin -n 50 --no-pager
                  exit 1
                fi

          - task: SSH@0
            displayName: 'Clean Up Old Backups'
            inputs:
              sshEndpoint: 'LendQube-Production-Server'
              runOptions: 'inline'
              inline: |
                echo "Cleaning up old backups..."
                ls -t /var/www/html/admin_backup_* 2>/dev/null | tail -n +6 | xargs -r rm -rf
                echo "Backup cleanup completed"

          - task: SSH@0
            displayName: 'Deployment Summary'
            inputs:
              sshEndpoint: 'LendQube-Production-Server'
              runOptions: 'inline'
              inline: |
                echo "Admin Website Deployment Summary"
                echo "================================"
                echo "Admin Website Status: $(systemctl is-active lendqube-admin)"
                echo "Deployment completed at: $(date)"
                echo "================================"
