﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.DbContexts;
using LendQube.Infrastructure.Core.Database.Repository.Concurrency;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Database.Repository;

internal sealed partial class RelationalDbRepository(AppDbContext context, ICurrentUserInfoProvider userInfo, ILogManager<IRelationalDbRepository> logger) : IRelationalDbRepository
{
    private readonly DbContextConcurrencyManager concurrencyManager = new(logger);
    public DbContextConcurrencyManager ConcurrencyManager => concurrencyManager;

    private DbSet<T> GetDbSet<T>() where T : class, IBaseEntityForRelationalDb => context.Set<T>();

    // Only to be used for querying unrelated entities. Any other usage is a code smell and will be flagged
    public IQueryable<T> Queryable<T>() where T : class, IBaseEntityForRelationalDb => GetDbSet<T>();

    public (bool, string) IsEntityValid<T>(T data) where T : class
    {
        var validationContext = new ValidationContext(data);
        var results = new List<ValidationResult>();
        var valid = Validator.TryValidateObject(data, validationContext, results, true);
        var resultString = string.Empty;
        if (!valid)
        {
            resultString = string.Join(",", results.Select(x => x.MemberNames.FirstOrDefault() + ": " + x.ErrorMessage));

            logger.LogWarning(EventSource.Infrastructure, EventAction.Validation, message: $"Invalid entity object {typeof(T).Name}: Errors - {resultString}", data: data);

        }
        return (valid, resultString);
    }
}
