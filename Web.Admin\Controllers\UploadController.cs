﻿using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.FileManagement;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Navigation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace LendQube.Web.Admin.Controllers;

[Authorize]
public class UploadController(IFileManagementService service) : Controller
{

    [Authorize(Policy = MessagingNavigation.MessagingTemplateIndexPermission), HttpPost("upload/messagetemplates")]
    public async Task<IActionResult> UploadMessageTemplateImages(IFormFile file, CancellationToken ct)
    {
        try
        {
            var fileUrl = await service.SaveFormFile($"{MessageConfigHelper.FileService}", $"templateimages/{SecurityDriven.FastGuid.NewGuid()}", file, ct);

            return Ok(new { Url = fileUrl });
        }
        catch (Exception ex)
        {
            return StatusCode(500, ex.Message);
        }
    }

    [Authorize(Policy = ManageCustomersNavigation.CustomerProfileSendCustomMessagePermission), HttpPost("upload/custommesageimages")]
    public async Task<IActionResult> UploadCustomMessageImages(IFormFile file, CancellationToken ct)
    {
        try
        {
            var fileUrl = await service.SaveFormFile($"{MessageConfigHelper.FileService}", $"custommessageimages/{SecurityDriven.FastGuid.NewGuid()}", file, ct);

            return Ok(new { Url = fileUrl });
        }
        catch (Exception ex)
        {
            return StatusCode(500, ex.Message);
        }
    }

}
