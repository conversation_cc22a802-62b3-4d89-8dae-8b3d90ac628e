﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Collection.Analytics;
using LendQube.Infrastructure.Collection.Payments;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Collection.ViewModels.PlacementData;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Medallion.Threading;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Schedules;

internal static class ApplyPaymentHandler
{
    public static async Task<Result<TransactionResultVM>> ConfirmPayment(IUnitofWork uow, IClock clock, IDistributedLockProvider distributedLockProvider,
        PaymentFactory paymentFactory, string userId, ProcessTransactionRequestVM vm, CancellationToken ct, MessageBrick message = null)
    {
        var data = await paymentFactory.ConfirmTransaction(userId, vm, ct);
        if (data.IsSuccessful)
        {
            await using var handle = await distributedLockProvider.AcquireLockAsync($"{nameof(ConfirmPayment)}_{userId}", cancellationToken: ct);

            var type = data.Data.Transaction.UserData.Get(TransactionHelper.TransactionType);
            if (type == RepaymentTransactionType.AmendSchedule.ToString() && (data.Data.Status == TransactionStatus.Successful || (data.Data.Type == PaymentType.SetupCard && data.Data.Status == TransactionStatus.Refunded)))
                await Reschedule(uow, clock, data.Data.Transaction, ct, message);

            if (data.Data.Type == PaymentType.SetupCard && data.Data.Status == TransactionStatus.Refunded) //new user, create schedule
            {
                await CreateNewSchedule(uow, userId, data.Data.Transaction, ct, message);
            }
            else
            {
                await ApplyPayment(uow, clock, data.Data.Transaction, ct, message);
            }
        }

        return data;
    }

    public static async Task CreateNewSchedule(IUnitofWork uow, string userId, Transaction txn, CancellationToken ct, MessageBrick message = null)
    {
        var profile = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerScheduleVM>.Where(x => x.Id == userId).Select(CustomerScheduleVM.FirstTimeScheduleMapping), ct);

        if (profile.Schedules.IsNullOrEmpty())
        {
            decimal scheduleAmount = 0;
            var amount = scheduleAmount = decimal.Parse(txn.UserData.Get(nameof(RequestScheduleVM.Amount)));
            var startDate = txn.UserData.Get(nameof(RequestScheduleVM.StartDate)).ParseFromString();
            var frequency = txn.UserData.Get(nameof(RequestScheduleVM.Frequency)).ToEnum<SchedulePaymentFrequency>();

            var noOfPayments = (int)Math.Ceiling(profile.BalanceRemaining / amount);

            var balanceLeft = profile.BalanceRemaining;
            var schedulesToCreate = new List<CustomerSchedule>();
            for (int i = 0; i < noOfPayments; i++)
            {
                amount = amount < balanceLeft ? amount : balanceLeft;
                var date = frequency.GetDate(startDate, i);
                var schedule = new CustomerSchedule
                {
                    ProfileId = profile.Id,
                    Period = i + 1,
                    Amount = amount,
                    DueDate = date,
                    CPADate = date,
                    PeriodStatus = SchedulePeriodStatus.NotDue,
                    PaymentStatus = SchedulePaymentStatus.NotPaid
                };
                schedulesToCreate.Add(schedule);
                balanceLeft -= amount;
            }

            uow.Db.InsertBulk(schedulesToCreate, ct);
            uow.Db.Insert(new CustomerActivity { ProfileId = profile.Id, Title = "New Schedule", Activity = "Schedule created" });
            await uow.SaveAsync(ct);

            await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == userId, x => x.SetProperty(y => y.PaymentFrequency, frequency), ct);
            await uow.Db.DeleteAndSaveWithFilterAsync<CustomerTransaction>(x => x.ProfileId == userId && x.TransactionId == txn.Id, ct);

            var hasMessage = message != null;
            message ??= MessageBuilder.New("New Schedule", txn.CreatedByUserId);
            message.Message(MessageConfigNames.NewSchedule.GetDisplayName())
                .WithRecipient(profile.Id,
                [
                    new($"{MessageTemplateKeys.Frequency}", $"{frequency}"),
                    new($"{MessageTemplateKeys.Amount}", $"{txn.CurrencySymbol}{scheduleAmount:n2}"),
                    new($"{MessageTemplateKeys.Duration}", $"{noOfPayments} {frequency.ToString().Replace("ly", "s")}"),
                ]);

            if (!hasMessage)
                _ = await message.Send(uow, ct);

            await ManageAnalyticsService.UpdateTotalCustomersWithSchedules(uow, ct);
        }
    }

    public static async Task ApplyPayment(IUnitofWork uow, IClock clock, Transaction txn, CancellationToken ct, MessageBrick message = null)
    {
        var customerTxn = await uow.Db.OneAsync(Query<CustomerTransaction>.Where(x => x.ProfileId == txn.ProfileId && x.TransactionId == txn.Id), ct);
        if (customerTxn == null || customerTxn.PaymentApplied || txn.Status != TransactionStatus.Successful)
        {
            if (customerTxn == null)
            {
                customerTxn = new CustomerTransaction
                {
                    ProfileId = txn.ProfileId,
                    AmountTried = txn.Amount,
                    TransactionId = txn.Id,
                    PaymentProvider = txn.Provider,
                };

                uow.Db.Insert(customerTxn);
                await uow.SaveAsync(ct);
            }
            else
            {
                if (customerTxn is { PaymentApplied: true } && txn.Status == TransactionStatus.Successful)
                {
                    txn.Status = TransactionStatus.Completed;
                    uow.Db.Update(txn);
                    await uow.SaveAsync(ct);
                }

                return;
            }
        }

        var type = txn.UserData.Get(TransactionHelper.TransactionType);
        if (type == RepaymentTransactionType.RecalculateRepayment.ToString())
            await ApplyPaymentAndRecalculateSchedule(uow, clock, customerTxn, txn, ct, message);
        else
            await ApplyPaymentToSchedule(uow, customerTxn, txn, ct, message);

        await ManageAnalyticsService.UpdateTotalPlacementValuePaid(uow, txn.TotalAmountPaid, ct);
    }

    public static async Task Reschedule(IUnitofWork uow, IClock clock, Transaction txn, CancellationToken ct, MessageBrick message = null)
    {
        var profile = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerScheduleVM>.Where(x => x.Id == txn.ProfileId).Select(CustomerScheduleVM.Mapping), ct);

        var deletedCount = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerSchedule>(x => x.ProfileId == profile.Id && x.PaymentStatus != SchedulePaymentStatus.Paid, ct);

        var amountToSet = decimal.Parse(txn.UserData.Get(nameof(RequestScheduleVM.Amount)));
        var startDate = txn.UserData.Get(nameof(RequestScheduleVM.StartDate)).ParseFromString();
        var frequency = txn.UserData.Get(nameof(RequestScheduleVM.Frequency)).ToEnum<SchedulePaymentFrequency>();

        //delete unpaid schedules and redistribute amount - allow to happen only once
        var noOfPayments = (int)Math.Ceiling(profile.BalanceRemaining / amountToSet);
        var balanceLeft = profile.BalanceRemaining;
        var schedulesToCreate = new List<CustomerSchedule>();
        var periodToStartAt = profile.Schedules.Count() - deletedCount;
        for (int i = 0; i < noOfPayments; i++)
        {
            var amount = amountToSet < balanceLeft ? amountToSet : balanceLeft;
            var date = frequency.GetDate(startDate, i);
            var today = DateOnly.FromDateTime(DateTime.UtcNow);

            var schedule = new CustomerSchedule
            {
                ProfileId = profile.Id,
                Period = periodToStartAt + 1 + i,
                Amount = amount,
                DueDate = date,
                CPADate = date,
                PeriodStatus = date == today ? SchedulePeriodStatus.Due : SchedulePeriodStatus.NotDue,
                PaymentStatus = SchedulePaymentStatus.NotPaid
            };
            schedulesToCreate.Add(schedule);
            balanceLeft -= amount;
        }

        uow.Db.InsertBulk(schedulesToCreate, ct);
        uow.Db.Insert(new CustomerActivity { ProfileId = profile.Id, Title = "Re-Scheduling", Activity = $"Account schedule updated. {frequency} payment set" });
        await uow.SaveAsync(ct);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == profile.Id, x => x
        .SetProperty(y => y.TotalRescheduleCount, y => y.TotalRescheduleCount + 1)
        .SetProperty(y => y.LastRescheduleDate, clock.GetCurrentInstant())
        .SetProperty(y => y.PaymentFrequency, frequency),
        ct);

        message ??= MessageBuilder.New("Amend Schedule", profile.Id);
        _ = await message
            .Message(MessageConfigNames.AmendSchedule.GetDisplayName())
            .WithRecipient(profile.Id,
            [
                new($"{MessageTemplateKeys.Frequency}", $"{frequency}"),
                new($"{MessageTemplateKeys.Amount}", $"{profile.CurrencySymbol}{amountToSet:n2}"),
                new($"{MessageTemplateKeys.Duration}", $"{noOfPayments} {frequency.ToString().Replace("ly", "s")}"),
            ])
            .Send(uow, ct);
    }

    private static async Task ApplyPaymentToSchedule(IUnitofWork uow, CustomerTransaction customerTxn, Transaction txn, CancellationToken ct, MessageBrick message = null)
    {
        List<string> schedulesAffected = [];

        var balanceToWorkWith = txn.TotalAmountPaid;
        var outstandingSchedules = await uow.Db.ManyAsync(Query<CustomerSchedule>.Where(x => x.ProfileId == customerTxn.ProfileId && x.PaymentStatus != SchedulePaymentStatus.Paid), ct);
        if (outstandingSchedules.IsNullOrEmpty())
        {
            var profile = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerScheduleVM>.Where(x => x.Id == customerTxn.ProfileId).Select(CustomerScheduleVM.FirstTimeScheduleMapping), ct);

            var startDate = txn.UserData.Get(nameof(RequestScheduleVM.StartDate)).ParseFromString();
            var frequency = txn.UserData.Get(nameof(RequestScheduleVM.Frequency)).ToEnum<SchedulePaymentFrequency>();

            var noOfPayments = (int)Math.Ceiling(profile.BalanceRemaining / txn.TotalAmountPaid);
            var balanceLeft = profile.BalanceRemaining;

            var schedulesToCreate = new List<CustomerSchedule>();

            for (int i = 0; i < noOfPayments; i++)
            {
                var date = frequency.GetDate(startDate, i);
                var schedule = new CustomerSchedule
                {
                    ProfileId = profile.Id,
                    Period = i + 1,
                    Amount = txn.TotalAmountPaid,
                    AmountPaid = i == 0 ? txn.TotalAmountPaid : 0,
                    DueDate = date,
                    CPADate = date,
                    PeriodStatus = i == 0 ? SchedulePeriodStatus.Due : SchedulePeriodStatus.NotDue,
                    PaymentStatus = i == 0 ? SchedulePaymentStatus.Paid : SchedulePaymentStatus.NotPaid
                };

                schedulesToCreate.Add(schedule);
                balanceLeft -= txn.TotalAmountPaid;

                if (balanceLeft <= 0)
                    break;
            }
            schedulesAffected.Add("1");
            uow.Db.InsertBulk(schedulesToCreate, ct);
            uow.Db.Insert(new CustomerActivity { ProfileId = profile.Id, Title = "New Schedule", Activity = "Schedule created" });
        }
        else
        {
            List<CustomerSchedule> modifiedSchedules = [];
            foreach (var item in outstandingSchedules.OrderBy(x => x.Period))
            {
                var amountToApply = item.Balance > balanceToWorkWith ? balanceToWorkWith : item.Balance;
                item.AmountPaid += amountToApply;
                item.PaymentStatus = item.AmountPaid >= item.Amount ? SchedulePaymentStatus.Paid : SchedulePaymentStatus.PartiallyPaid;

                schedulesAffected.Add(item.Period.ToString());
                balanceToWorkWith -= amountToApply;
                modifiedSchedules.Add(item);

                if (balanceToWorkWith <= 0)
                    break;
            }

            uow.Db.UpdateBulk(modifiedSchedules, ct);
        }

        var paymentMethod = txn.UserData.Get(TransactionHelper.PaymentMethod);
        customerTxn.PaymentMethod = paymentMethod;
        customerTxn.SchedulePeriodAffected = schedulesAffected;
        customerTxn.Successful = true;
        customerTxn.PaymentApplied = true;
        customerTxn.AmountPaid = txn.TotalAmountPaid;
        customerTxn.PaymentType = "Scheduled Payment";

        uow.Db.Update(customerTxn);
        uow.Db.Insert(new CustomerActivity
        {
            ProfileId = customerTxn.ProfileId,
            Title = "Payment made",
            Activity = $"Payment of {txn.CurrencySymbol}{txn.TotalAmountPaid:n2} made",
            CreatedByUserId = txn.CreatedByUserId,
        });

        txn.Status = TransactionStatus.Completed;
        uow.Db.Update(txn);

        var affectedPlacements = await RedistributePaymentToPlacements(uow, customerTxn, ct);
        await uow.SaveAsync(ct);
        await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == customerTxn.ProfileId, x => x.SetProperty(y => y.BalancePaid, y => y.BalancePaid + txn.TotalAmountPaid), ct);

        var hasMessage = message != null;
        message ??= MessageBuilder.New("Schedule Payment", txn.CreatedByUserId);

        var nonSettled = affectedPlacements.Where(x => x.Status < PlacementStatus.Settled);
        if (!nonSettled.IsNullOrEmpty())
        {
            var balance = await uow.Db.OneSelectAsync(Query<CustomerProfile, decimal>.Where(x => x.Id == customerTxn.ProfileId).Select(x => x.BalanceRemaining), ct);
            message
                .Message(MessageConfigNames.PaymentReceived.GetDisplayName())
                .WithRecipient(customerTxn.ProfileId,
                [
                    new($"{MessageTemplateKeys.Amount}", $"{txn.CurrencySymbol}{txn.TotalAmountPaid:n2}"),
                    new($"{MessageTemplateKeys.Balance}", $"{txn.CurrencySymbol}{balance:n2}"),
                    new($"{MessageTemplateKeys.Currency}", txn.CurrencySymbol),
                    new($"{MessageTemplateKeys.CompanyName}", string.Join(", ", nonSettled.Select(x => x.Company))),
                ]);
        }

        var settled = affectedPlacements.Where(x => x.Status == PlacementStatus.Settled);
        if (!settled.IsNullOrEmpty())
        {
            message
                .Message(MessageConfigNames.PaymentSettled.GetDisplayName())
                .WithRecipient(customerTxn.ProfileId,
                [
                    new($"{MessageTemplateKeys.Amount}", $"{txn.CurrencySymbol}{txn.TotalAmountPaid:n2}"),
                    new($"{MessageTemplateKeys.Currency}", txn.CurrencySymbol),
                    new($"{MessageTemplateKeys.CompanyName}", string.Join(", ", settled.Select(x => x.Company))),
                ]);

            _ = await ManageAnalyticsService.UpdateTotalSettledPlacements(uow, settled.Count(), ct);
        }

        if (!hasMessage)
            _ = await message.Send(uow, ct);
    }

    private static async Task ApplyPaymentAndRecalculateSchedule(IUnitofWork uow, IClock clock, CustomerTransaction customerTxn, Transaction txn, CancellationToken ct, MessageBrick message = null)
    {
        var profile = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerScheduleVM>.Where(x => x.Id == customerTxn.ProfileId && x.BalanceRemaining > 0).Select(CustomerScheduleVM.RecalculateScheduleMapping), ct);
        var outstandingSchedules = profile.Schedules.Where(x => x.PaymentStatus != SchedulePaymentStatus.Paid).OrderBy(x => x.Period).ToList();

        var createdSchedules = new List<CustomerSchedule>();

        if (!outstandingSchedules.IsNullOrEmpty())
        {
            _ = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerSchedule>(x => x.ProfileId == customerTxn.ProfileId && x.PaymentStatus != SchedulePaymentStatus.Paid, ct);

            var balanceLeft = profile.BalanceRemaining - txn.TotalAmountPaid;
            if (balanceLeft > 0)
            {
                var createdSchedule = outstandingSchedules[0];

                var amountToDistribute = outstandingSchedules.TakeLast(3).GroupBy(x => x.Amount).OrderByDescending(x => x.Count()).FirstOrDefault().Select(x => x.Amount).FirstOrDefault();
                var noOfPayments = (int)Math.Ceiling(balanceLeft / amountToDistribute);

                var periodToStartAt = profile.Schedules.Count(x => x.PaymentStatus == SchedulePaymentStatus.Paid);
                for (int i = 0; i < noOfPayments; i++)
                {
                    var amount = amountToDistribute < balanceLeft ? amountToDistribute : balanceLeft;
                    var date = profile.PaymentFrequency.Value.GetDate(createdSchedule.DueDate, i);
                    var schedule = new CustomerSchedule
                    {
                        ProfileId = profile.Id,
                        Period = periodToStartAt + 1 + i,
                        Amount = amount,
                        DueDate = date,
                        CPADate = date,
                        PeriodStatus = SchedulePeriodStatus.NotDue,
                        PaymentStatus = SchedulePaymentStatus.NotPaid
                    };
                    createdSchedules.Add(schedule);
                    balanceLeft -= amount;

                    if (balanceLeft <= 0)
                        break;
                }

                uow.Db.InsertBulk(createdSchedules, ct);
            }
        }

        var paymentMethod = txn.UserData.Get(TransactionHelper.PaymentMethod);
        customerTxn.PaymentMethod = paymentMethod;
        customerTxn.Successful = true;
        customerTxn.PaymentApplied = true;
        customerTxn.AmountPaid = txn.TotalAmountPaid;
        customerTxn.PaymentType = "One-off Payment";

        uow.Db.Update(customerTxn);
        uow.Db.Insert(new CustomerActivity
        {
            ProfileId = customerTxn.ProfileId,
            Title = createdSchedules.Count > 0 ? "Re-Scheduling" : "Payment made",
            Activity = $"Lump sum of {txn.TotalAmountPaid:n2} paid{(createdSchedules.Count > 0 ? " and placement rescheduled" : "")}",
            CreatedByUserId = txn.CreatedByUserId,
        });

        txn.Status = TransactionStatus.Completed;
        uow.Db.Update(txn);

        var affectedPlacements = await RedistributePaymentToPlacements(uow, customerTxn, ct);
        await uow.SaveAsync(ct);


        await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == customerTxn.ProfileId, x => x
        .SetProperty(y => y.BalancePaid, y => y.BalancePaid + txn.TotalAmountPaid)
        .SetProperty(y => y.LastRescheduleDate, y => outstandingSchedules.Count > 0 ? clock.GetCurrentInstant() : y.LastRescheduleDate),
        ct);

        var hasMessage = message != null;
        message ??= MessageBuilder.New("Schedule Payment", txn.CreatedByUserId);

        var nonSettled = affectedPlacements.Where(x => x.Status < PlacementStatus.Settled);
        if (!nonSettled.IsNullOrEmpty())
        {
            var balance = await uow.Db.OneSelectAsync(Query<CustomerProfile, decimal>.Where(x => x.Id == customerTxn.ProfileId).Select(x => x.BalanceRemaining), ct);

            if (createdSchedules.Count > 0)
            {
                message
                    .Message(MessageConfigNames.PaymentReceivedAndRescheduled.GetDisplayName())
                    .WithRecipient(customerTxn.ProfileId,
                    [
                        new($"{MessageTemplateKeys.Amount}", $"{txn.CurrencySymbol}{txn.TotalAmountPaid:n2}"),
                    new($"{MessageTemplateKeys.Balance}", $"{txn.CurrencySymbol}{balance:n2}"),
                    new($"{MessageTemplateKeys.Currency}", txn.CurrencySymbol),
                    new($"{MessageTemplateKeys.Frequency}", $"{profile.PaymentFrequency}"),
                    new($"{MessageTemplateKeys.Duration}", $"{createdSchedules.Count} {profile.PaymentFrequency.ToString().Replace("ly", "s")}"),
                    new($"{MessageTemplateKeys.CompanyName}", string.Join(", ", nonSettled.Select(x => x.Company))),
                    ]);
            }
            else
            {
                message
                .Message(MessageConfigNames.PaymentReceived.GetDisplayName())
                .WithRecipient(customerTxn.ProfileId,
                [
                    new($"{MessageTemplateKeys.Amount}", $"{txn.CurrencySymbol}{txn.TotalAmountPaid:n2}"),
                    new($"{MessageTemplateKeys.Balance}", $"{txn.CurrencySymbol}{balance:n2}"),
                    new($"{MessageTemplateKeys.Currency}", txn.CurrencySymbol),
                    new($"{MessageTemplateKeys.CompanyName}", string.Join(", ", nonSettled.Select(x => x.Company))),
                ]);
            }
        }

        var settled = affectedPlacements.Where(x => x.Status == PlacementStatus.Settled);
        if (!settled.IsNullOrEmpty())
        {
            message
                .Message(MessageConfigNames.PaymentSettled.GetDisplayName())
                .WithRecipient(customerTxn.ProfileId,
                [
                    new($"{MessageTemplateKeys.Amount}", $"{txn.TotalAmountPaid:n2}"),
                    new($"{MessageTemplateKeys.Currency}", txn.CurrencySymbol),
                    new($"{MessageTemplateKeys.CompanyName}", string.Join(", ", settled.Select(x => x.Company))),
                ]);
            _ = await ManageAnalyticsService.UpdateTotalSettledPlacements(uow, settled.Count(), ct);
        }

        if (!hasMessage)
            _ = await message.Send(uow, ct);
    }

    private static async Task<List<Placement>> RedistributePaymentToPlacements(IUnitofWork uow, CustomerTransaction customerTxn, CancellationToken ct)
    {
        var newOrActivePlacements = await uow.Db.ManyAsync(Query<Placement>
            .Where(x => x.ProfileId == customerTxn.ProfileId && x.Status < PlacementStatus.Settled)
            .OrderBy(x => x.OrderBy(y => y.Id)), ct);

        var balance = customerTxn.AmountPaid;
        var modifiedPlacements = new List<Placement>();
        var placementTxns = new List<PlacementTransaction>();
        var activities = new List<PlacementActivity>();

        foreach (var placement in newOrActivePlacements)
        {
            PlacementTransaction placementTxn = new()
            {
                ProfileId = placement.ProfileId,
                PlacementId = placement.Id,
                TransactionId = customerTxn.TransactionId,
                PaymentProvider = customerTxn.PaymentProvider,
                PaymentMethod = customerTxn.PaymentMethod,
                PaymentType = customerTxn.PaymentType,
            };

            if (placement.BalanceRemaining < balance)
            {
                placement.BalancePaid += placement.BalanceRemaining;
                placementTxn.AmountPaid = placement.BalanceRemaining;
                balance -= placement.BalanceRemaining;
            }
            else if (placement.BalanceRemaining > balance || placement.BalanceRemaining == balance)
            {
                placement.BalancePaid += balance;
                placementTxn.AmountPaid = balance;
                balance = 0;
            }

            placement.Status = placement.BalanceTotal == placement.BalancePaid ?
                PlacementStatus.Settled : PlacementStatus.Active;

            modifiedPlacements.Add(placement);
            placementTxns.Add(placementTxn);
            activities.Add(new()
            {
                PlacementId = placement.Id,
                Title = "Payment applied",
                Activity = $"Amount {placementTxn.AmountPaid:n2} applied from transaction {customerTxn.TransactionId} total {customerTxn.AmountPaid:n2}",
                CreatedByUserId = customerTxn.CreatedByUserId,
            });

            if (balance == 0)
                break;
        }

        uow.Db.UpdateBulk(modifiedPlacements, ct);
        uow.Db.InsertBulk(placementTxns, ct);
        uow.Db.InsertBulk(activities, ct);

        return modifiedPlacements;
    }
}