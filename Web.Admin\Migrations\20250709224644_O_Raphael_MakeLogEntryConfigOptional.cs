﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class <PERSON>_<PERSON>_MakeLogEntryConfigOptional : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MessageLogEntry_MessageConfiguration_MessageConfigurationId",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.AlterColumn<long>(
                name: "MessageConfigurationId",
                schema: "core",
                table: "MessageLogEntry",
                type: "bigint",
                nullable: true,
                oldClrType: typeof(long),
                oldType: "bigint");

            migrationBuilder.AddForeignKey(
                name: "FK_MessageLogEntry_MessageConfiguration_MessageConfigurationId",
                schema: "core",
                table: "MessageLogEntry",
                column: "MessageConfigurationId",
                principalSchema: "core",
                principalTable: "MessageConfiguration",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MessageLogEntry_MessageConfiguration_MessageConfigurationId",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.AlterColumn<long>(
                name: "MessageConfigurationId",
                schema: "core",
                table: "MessageLogEntry",
                type: "bigint",
                nullable: false,
                defaultValue: 0L,
                oldClrType: typeof(long),
                oldType: "bigint",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_MessageLogEntry_MessageConfiguration_MessageConfigurationId",
                schema: "core",
                table: "MessageLogEntry",
                column: "MessageConfigurationId",
                principalSchema: "core",
                principalTable: "MessageConfiguration",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
