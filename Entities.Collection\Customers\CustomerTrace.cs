﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using NodaTime;

namespace LendQube.Entities.Collection.Customers;
public class CustomerTrace : BaseEntityWithIdentityId<CustomerTrace>
{
    [DbGuid]
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }

    public string RetrieverTraceDone { get; set; }
    public DateOnly? RetrieverTraceDate { get; set; }
    public string RetrieverTraceResult { get; set; }
    public string CCJVolume { get; set; }
    public string CCJValue { get; set; }
    public string HomeOwner { get; set; }
    public string ProceedWithLegal { get; set; }
    public string DODTraceDone { get; set; }
    public DateOnly? DODTraceDate { get; set; }
    public string DODTraceResult { get; set; }
    public string EndeavourTraceDone { get; set; }
    public DateOnly? EndeavourTraceDate { get;set; }
    public string EndeavourTraceResult { get; set; }
    public string OtherTraceDone { get; set; }
    public string OtherTraceCompanyName { get; set; }
    public DateOnly? OtherTraceCompanyDate { get; set; }
    public string OtherTraceResult { get; set; }    
}
