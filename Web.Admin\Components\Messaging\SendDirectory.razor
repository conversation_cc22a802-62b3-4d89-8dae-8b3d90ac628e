﻿@page "/messaging/directory"

@using BlazorInputTags
@using LendQube.Entities.Collection.Customers
@using LendQube.Entities.Core.Messaging
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.Helpers.Utils
@using LendQube.Infrastructure.Core.Messaging
@using LendQube.Infrastructure.Core.Messaging.Configuration
@using LendQube.Infrastructure.Core.ViewModels.Messaging
@using System.ComponentModel.DataAnnotations
@using OfficeOpenXml
@using Radzen.Blazor
@using System.Text.Json
@inherits GenericCrudVMTable<MessagingGroup, MessagingGroupVM>
@inject GenericVMSpecificationService<MessagingGroupEntry, MessagingGroupEntryVM> groupService
@inject MessagingGroupBuilder groupBuilder


@attribute [Authorize(Policy = MessagingNavigation.MessagingGroupIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
ModalCss="width-md">
    <BodyContent>
        @SingleFormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
ModalCss="width-md">
    <BodyContent>
        @SingleFormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalComponent Policy="@EditPermission" ModalId="@ViewModalName" ModalCss=" modal-dialog-large width-70vw" Title=@($"View {FormBaseTitle}: {Selected?.Name}")>
    <BodyContent>
        <StatusMessage Message="ModalMessage" />
        <DataTable T="MessagingGroupEntryVM" TableDefinition="GroupTableDefinition" EditRow="StartGroupEdit" DeleteRow="SubmitGroupDelete" LoadData="LoadEntries" DeferLoading="true" @ref="groupTable" />
    </BodyContent>
</ModalComponent>

<ModalEditComponent Policy="@EditPermission" Title="Add Contact" Model="AddGroupModel" OnValidSubmit="@SubmitGroupAdd" ModalId="@AddEntryModalName" ModalCss="width-xlg" ModalMessage="@GroupModalMessage" 
FormName="AddContactForm" onkeydown="return event.key != 'Enter';">
    <BodyContent>
        @GroupFormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" Title="Modify Contact" Model="EditGroupModel" OnValidSubmit="@SubmitGroupEdit" ModalId="@EditEntryModalName" ModalMessage="@GroupModalMessage" ModalCss="width-xlg" 
FormName="EditContactForm" onkeydown="return event.key != 'Enter';">
    <BodyContent>
        @GroupFormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" Title="Build New Group" Model="AddBuilderForm" OnValidSubmit="@SubmitBuilder" ModalId="@AddBuilderModalName" ModalMessage="@ModalMessage" ModalCss="modal-dialog-large width-70vw"
FormName="AddBuilderForm" onkeydown="return event.key != 'Enter';">
    <BodyContent>
        <div class="grid-col-2">

            <div class="form-row">
                <label class="form-label" for="Description">Description</label>
                <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
                <ValidationMessage For="() => context.Description" class="text-danger" />
            </div>

            <div class="form-row">
                <div class="check-group">
                    <label class="check-label">
                        Write Your Own Sql Query
                        <InputCheckbox class="check-input" @bind-Value="context.WriteOwnSqlQuery" />
                        <span class="checkmark"></span>
                    </label>
                </div>
            </div>
        </div>


        @if(!context.WriteOwnSqlQuery)
        {
            <div class="grid-col-2">

                @foreach (var block in context.Blocks)
                {
                    <div class="form-row">
                        <label class="form-label" for="TableName">Data Source</label>
                        <div class="input-group">
                            @if (block.Value.ShouldHaveCondition)
                            {
                                <InputSelect class="form-input" style="width: 70px" @bind-Value=@block.Value.Condition>
                                    <option selected>@FilterCondition.Or</option>
                                    <option>@FilterCondition.And</option>
                                </InputSelect>
                            }
                            <RadzenDropDown @bind-Value=@block.Value.TableName Data=@block.Value.TableNames Name="TableName" Change="() => OnDataSourceChanged(block.Key, block.Value)"
                            FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true"
                            Placeholder="Select data source" class="form-input" Style="flex: 1" />

                            <button class="btn btn__sm btn--link __icon btn__filter" type="button" data-bs-toggle="modal"
                            data-bs-target="#<EMAIL>()" style="margin-left: 5px">
                                Build Filter
                            </button>
                            @if (block.Value.ShouldHaveCondition)
                            {
                                <button class="btn btn--icon __danger" type="button" @onclick="() => DeleteBlock(block.Key, context)" style="margin-left: 5px">
                                    <span class="svg svg-trash-gray"></span>
                                </button>
                            }
                        </div>
                        <ValidationMessage For="() => block.Value.TableName" class="text-danger" />
                    </div>

                    <div class="modal fade" id="<EMAIL>()" tabindex="-1" aria-labelledby="<EMAIL>()"
                    aria-modal="true" role="dialog">
                        <div class="modal-dialog width-xlg filter-controls">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <div class="__title">
                                        <h5 class="modal-title" id="filterModalLabel_@block">Build Filter for @block.Value.TableName</h5>
                                    </div>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                                    aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form class="filter-items">
                                        <div class="filter-inputs">
                                            @foreach (var item in block.Value.FilterInputs)
                                            {
                                                <DataTableFilterItem @bind-FilterValue=@block.Value.FilterInputs FilterKey="item.Key" RemoveFilter="() => DeleteFilter(block.Value, item.Key)" Filters="block.Value.FilterDefinition" JSRuntime="JSRuntime"
                                                CancellationToken="Cancel" @key="@item.Key" />
                                            }

                                            <div class="add-row">
                                                <button class="btn btn__sm btn--gray __icon" type="button" @onclick="() => AddFilter(block.Value)">
                                                    <span class="svg svg-add"></span>Add filter
                                                </button>
                                            </div>
                                        </div>

                                        <div class="form-row __footer">
                                            <button class="btn btn--default" type="button" @onclick="() => ClearFilter(block.Value)">Reset</button>
                                            <button class="btn btn--success __secondary" data-bs-dismiss="modal" type="button" disabled="@(!block.Value.FilterInputs?.Values?.Any(x => x.IsValid))">
                                                Save
                                                Filter
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>

            <div class="grid-col-2">

                @if (ShowAddDataSource)
                {
                    <div class="form-row add-row">
                        <button class="btn btn--gray __icon" type="button" @onclick="() => AddBlock(context)">
                            <span class="svg svg-add"></span>Add Data Source
                        </button>
                    </div>
                }
                <div class="form-row">
                    <button class="btn btn--default" type="button" @onclick="() => ClearAllBlocks(context)">Reset</button>
                </div>
            </div>
        }

        <div class="grid-col-2">
            @if (!context.WriteOwnSqlQuery)
            {
                <div class="form-row">
                    <button class="btn btn--success" type="button" @onclick="() => GenerateQuery()" disabled="@(!context?.Blocks?.Values?.Any(x => x.IsValid))">
                        Generate Query
                    </button>
                </div>
            }
            @if (!string.IsNullOrEmpty(context.Query))
            {
                <div class="form-row">
                    <button class="btn btn--gray" type="button" @onclick="() => PreviewBuilderData()">
                        Preview Data
                    </button>
                </div>
            }
        </div>

        @if (!string.IsNullOrEmpty(context.Query) || context.WriteOwnSqlQuery)
        {
            <div class="form-row">
                <label class="form-label" for="Query">Query</label>
                @if(context.WriteOwnSqlQuery)
                {
                    <InputTextArea @bind-Value=context.Query rows="6" />
                }
                else
                {
                    <InputTextArea @bind-Value=context.Query rows="6" readonly />
                }

                <ValidationMessage For="() => context.Query" class="text-danger" />
                @if (context.WriteOwnSqlQuery)
                {
                    <small class="text_sm_medium text_small">Query must return FullName and Email columns only from the CustomerProfile table. Preview to confirm query is correct</small>
                }
                else
                {
                    <small class="text_sm_medium text_small">Query generated from builder. Preview to view matching data. Select <strong>Write Your Own Sql Query</strong> to add modifications</small>
                }

            </div>

            @if (context.WriteOwnSqlQuery && context.CustomQueryValidated)
            {
                <div class="form-row">
                    <label class="form-label" for="CustomQuery">Executed Query</label>
                    <InputTextArea @bind-Value=context.CustomQuery rows="6" />
                    <ValidationMessage For="() => context.CustomQuery" class="text-danger" />
                    <small class="text_sm_medium text_small">Should be the same as Query but returns only CustomerProfile Id column</small>
                </div>
            }
        }

    </BodyContent>
</ModalEditComponent>


<ModalComponent Policy="@EditPermission" ModalId="@PreviewBuilderModalName" ModalCss="modal-dialog-large width-70vw" Title=@($"Preview Query Data: {Selected?.Name}")>

    <BodyContent>
        <StatusMessage Message="ModalMessage" />

        <DataTable T="MessageGroupUserForBuilder" TableDefinition="@previewTableDefinition" LoadData="LoadBuilderPreview" DeferLoading="true" DefaultPageSize="5" @ref="groupPreviewTable" />

    </BodyContent>
</ModalComponent>
@code
{
    [SupplyParameterFromForm]
    private MessagingGroupEntryVM AddGroupModel { get; set; } = new();

    [SupplyParameterFromForm]
    private MessagingGroupEntryVM EditGroupModel { get; set; } = new();

    private MessagingGroupVM Selected;
    private DataTable<MessagingGroupEntryVM> groupTable;
    private ColumnList GroupTableDefinition { get; set; }
    private string AddEntryModalName => "AddEntryModalName";
    private string EditEntryModalName => "EditEntryModalName";
    
    private ColumnList previewTableDefinition;

    private StatusMessageBuilder GroupModalMessage = new();

    private RenderFragment<MessagingGroup> SingleFormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
    </div>
    ;

    private RenderFragment<MessagingGroupEntryVM> GroupFormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Name">Emails</label>
            <InputTags Value="context.Emails" ValidateTag="ValidateEmailAsync" Options='new InputTagOptions { DisplayLabel = false, InputPlaceholder = "Contact Emails" }' />
            <ValidationMessage For="() => context.Emails" class="text-danger" />
            <small class="text_sm_medium text_small">After typing, press enter to add. You can add multiple</small>
        </div>

        <div class="form-row">
            <label class="form-label" for="Name">Phone numbers</label>
            <InputTags Value="context.PhoneNumberInput" ValidateTag="ValidatePhoneNumberAsync" Options='new InputTagOptions { DisplayLabel = false, InputPlaceholder = "format: +234 8012301234" }' />
            <ValidationMessage For="() => context.PhoneNumberInput" class="text-danger" />
            <small class="text_sm_medium text_small">After typing, press enter to add. You can add multiple</small>
        </div>
    </div>
    ;

    protected override void OnInitialized()
    {
        Title = "Send Directory";
        SubTitle = "Contact Groups";
        FormBaseTitle = "Contact Group";
        CreatePermission = MessagingNavigation.MessagingGroupCreatePermission;
        EditPermission = MessagingNavigation.MessagingGroupEditPermission;
        DeletePermission = MessagingNavigation.MessagingGroupDeletePermission;
        QuerySelector = MessagingGroupVM.Mapping;

        GroupTableDefinition = groupService.CrudService.GetTableDefinition(new() { HasEdit = true, HasDelete = true });
        GroupTableDefinition.TopActionButtons.Add(new TopActionButton(ModalName: AddEntryModalName, Action: () => {
            AddGroupModel = new();
            return CloseMessage(GroupModalMessage);
        }));

    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        AddRowButton(EditPermission, new RowActionButton("View", Icon: "eye", Action: async (object row) =>
        {
            CloseMessage();
            table.Loading = true;

            ModalMessage.Close();
            GroupModalMessage.Close();

            Selected = row as MessagingGroupVM;
            await groupTable.LoadElement();
            table.Loading = false;
            StateHasChanged();

            await JSRuntime.OpenModal(ViewModalName, Cancel);
        }));

        AddRowButton(EditPermission, new("Build Query", Icon: "table", Action: async (object row) =>
        {
            CloseMessage();
            table.Loading = true;

            ModalMessage.Close();
            GroupModalMessage.Close();

            Selected = row as MessagingGroupVM;
            await StartBuild();

            table.Loading = false;
            StateHasChanged();

            await JSRuntime.OpenModal(AddBuilderModalName, Cancel);
        }));


        previewTableDefinition = groupBuilder.GetTableDefinition();

        AddTopButton(previewTableDefinition, new TopActionButton
        {
            Name = "Export To Excel",
            Icon = "file-text", 
            ButtonClass = "btn--success",
            Action = async () => await ExportToExcel()
        });
        AddTopButton(previewTableDefinition, new TopActionButton
         {
                Name = "Export To CSV",
                Icon = "file-text",
                ButtonClass = "btn--success",
                Action = async () => await ExportToCsv()
            });


    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter)
        || x.Directory.Any(y => EF.Functions.ILike(y.Name, filterAndPage.TextFilter))
        || x.Directory.Any(y => y.Emails.Contains(filterAndPage.TextFilter))
        || x.Directory.Any(y => y.PhoneNumbers
        .Any(z => EF.Functions.Like(z.Number, filterAndPage.TextFilter)));
    }

    protected override ValueTask StartEdit(MessagingGroupVM data, CancellationToken ct) => Edit(() => Task.FromResult(data.Get()), ct);
    protected override ValueTask<bool> SubmitDelete(MessagingGroupVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);


    #region GroupEntry

    private ValueTask<TypedBasePageList<MessagingGroupEntryVM>> LoadEntries(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        groupService.PrimaryCriteria = x => x.MessagingGroupId == Selected.Id;

        if(!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            groupService.PrimaryCriteria = groupService.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter)
            || x.Emails.Contains(filterAndPage.TextFilter)
            || x.PhoneNumbers.Any(z => EF.Functions.Like(z.Number, filterAndPage.TextFilter))
            );
        }


        return groupService.CrudService.GetTypeBasedPagedData(groupService, filterAndPage, MessagingGroupEntryVM.Mapping,  ct: ct);
    }

    private Task<bool> ValidateEmailAsync(string tag)
    {
        bool result = !string.IsNullOrEmpty(tag) && new EmailAddressAttribute().IsValid(tag);
        return Task.FromResult(result);
    }

    private Task<bool> ValidatePhoneNumberAsync(string tag)
    {
        var tagSplit = tag.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        bool result = tagSplit.Count() == 2 && tagSplit[0].StartsWith("+");
        return Task.FromResult(result);
    }


    private async Task ExportToCsv()
    {
        ExcelPackage.License.SetNonCommercialPersonal(StringConstants.ExcelLicenseName);

        var result = groupBuilder.GetPreviewDataQueryable(AddBuilderForm.Query);

        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Preview Data");

        worksheet.Cells["A1"].LoadFromCollection(result, true);

        worksheet.Cells.AutoFitColumns();

        var start = worksheet.Dimension.Start;
        var end = worksheet.Dimension.End;

        var format = new ExcelOutputTextFormat
        {
                Delimiter = ',',       
                FirstRowIsHeader = true,
                TextQualifier = '"',   
        };

        using var csvStream = new MemoryStream();
        await worksheet.Cells[$"{start.Address}:{end.Address}"].SaveToTextAsync(csvStream, format);

        csvStream.Position = 0;
        var bytes = csvStream.ToArray();
        var base64 = Convert.ToBase64String(bytes);
        var dataUrl = $"data:text/csv;base64,{base64}";

        await JSRuntime.InvokeVoidAsync("blazorExtensions.TriggerFileDownload", "PreviewData.csv", dataUrl);
    }


    private async Task ExportToExcel()
    {
        ExcelPackage.License.SetNonCommercialPersonal(StringConstants.ExcelLicenseName);
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Preview Data");

        var result = groupBuilder.GetPreviewDataQueryable(AddBuilderForm.Query); 

        worksheet.Cells["A1"].LoadFromCollection(result,true);

        worksheet.Cells[1, 1].Value = "Full Name";
        worksheet.Cells[1, 2].Value = "Email Address";
        worksheet.Cells[1, 3].Value = "Account Id";
        worksheet.Cells[1, 4].Value = "Phone Number";
        worksheet.Cells[1, 5].Value = "Mobile Number";
        worksheet.Cells[1, 1, 1, 5].Style.Font.Bold = true;

        worksheet.Cells.AutoFitColumns();

        var stream = new MemoryStream();
        package.SaveAs(stream);
        var bytes = stream.ToArray();
        var base64 = Convert.ToBase64String(bytes);
        var dataUrl = $"data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{base64}";
        await JSRuntime.InvokeVoidAsync("blazorExtensions.TriggerFileDownload", "PreviewData.xlsx", dataUrl);
    }

    protected async ValueTask SubmitGroupAdd()
    {
        if(!AddGroupModel.IsValid)
        {
            GroupModalMessage.Error("Please provide at least one email or phone number");
            return;
        }

        ModalMessage.Close();
        GroupModalMessage.Close();

        var result = await groupService.CrudService.New(AddGroupModel.Get(Selected.Id), Cancel);
        if (result)
        {
            await JSRuntime.CloseModal(AddEntryModalName, Cancel);
            await groupTable.Refresh();
            await table.Refresh();
            AddGroupModel = new();
            ModalMessage.Success("Data saved successfully");
        }
        else
        {
            GroupModalMessage.Error("Saving data failed");
        }
        StateHasChanged();
    }

    protected async ValueTask StartGroupEdit(MessagingGroupEntryVM data, CancellationToken ct)
    {
        ModalMessage.Close();
        GroupModalMessage.Close();

        var model = data.DeepCopy();
        model.PhoneNumberInput = model.PhoneNumbersFormatted?.Split(", ")?.ToList() ?? [];
        EditGroupModel = model;
        StateHasChanged();
        if (!ct.IsCancellationRequested)
            await JSRuntime.OpenModal(EditEntryModalName, Cancel);
    }

    protected async ValueTask SubmitGroupEdit()
    {
        if (!EditGroupModel.IsValid)
        {
            GroupModalMessage.Error("Please provide at least one email or phone number");
            return;
        }

        ModalMessage.Close();
        GroupModalMessage.Close();

        var result = await groupService.CrudService.Update(EditGroupModel.Get(Selected.Id), Cancel);
        if (result)
        {
            await JSRuntime.CloseModal(EditEntryModalName, Cancel);
            await groupTable.Refresh();
            EditGroupModel = new();
            ModalMessage.Success("Data saved successfully");
        }
        else
        {
            GroupModalMessage.Error("Saving data failed");
        }

        StateHasChanged();
    }

    protected async ValueTask<bool> SubmitGroupDelete(MessagingGroupEntryVM data, Func<Task> refresh, CancellationToken ct)
    {
        var result = await groupService.CrudService.Delete(x => x.Id == data.Id, ct);
        await refresh();
        await table.Refresh();
        if (result)
        {
            ModalMessage.Success("Data deleted successfully");
        }
        else
        {
            ModalMessage.Error("Deleting data failed");
        }
        StateHasChanged();
        return result;
    }
    #endregion

    #region GroupBuilder

    private string AddBuilderModalName => "AddBuilderModalName";
    private string PreviewBuilderModalName => "PreviewBuilderModalName";
    private Dictionary<string, List<ColumnFilter>> definitions = [];

    private bool ShowAddDataSource { get; set; } = true;

    [SupplyParameterFromForm]
    public MessagingGroupBuilderVM AddBuilderForm { get; set; } = new();


    private DataTable<MessageGroupUserForBuilder> groupPreviewTable;

    private async Task StartBuild()
    {
        definitions = groupBuilder.GetAllRelatedTablesAndColumns<CustomerProfile>();
        AddBuilderForm = new()
        {
            TableNames = definitions.Keys.ToList(),
        };


        var existing = await groupService.CrudService.Db.OneAsync(Query<MessagingGroupQuery>.Where(x => x.MessagingGroupId == Selected.Id), Cancel);
        if(existing != null)
        {
            AddBuilderForm.Id = existing.Id;
            AddBuilderForm.Description = existing.Description;
            AddBuilderForm.Query = existing.Query;
            if(!string.IsNullOrEmpty(existing.BuildingBlock))
            {
                var blocks = JsonSerializer.Deserialize<Dictionary<Guid, MessageGroupBuilderBlock>>(existing.BuildingBlock);
                List<string> loadedTableNames = [];
                foreach (var item in blocks)
                {
                    item.Value.TableNames = AddBuilderForm.TableNames.Where(x => !loadedTableNames.Contains(x)).ToList();
                    if (definitions.TryGetValue(item.Value.TableName, out var filterDefinition))
                    {
                        item.Value.FilterDefinition = filterDefinition;
                    }
                    loadedTableNames.Add(item.Value.TableName);
                }
                AddBuilderForm.Blocks = blocks;
            }
            else
            {
                AddBuilderForm.WriteOwnSqlQuery = true;
                AddBuilderForm.CustomQueryValidated = true;
                AddBuilderForm.CustomQuery = existing.SenderQuery;
            }

            StateHasChanged();
        }
        else
        {
            AddBuilderForm.Blocks[SecurityDriven.FastGuid.NewGuid()] = new()
            {
                FilterDefinition = definitions[AddBuilderForm.TableNames[0]],
                    FilterInputs = new() { { SecurityDriven.FastGuid.NewGuid(), new ComplexFilter() } },
                TableName = AddBuilderForm.TableNames[0],
                TableNames = AddBuilderForm.TableNames
            };
        }

        ShowAddDataSource = true;
    }

    private void OnDataSourceChanged(Guid key, MessageGroupBuilderBlock block)
    {
        var oldBlock = AddBuilderForm.Blocks.FirstOrDefault(x => x.Key != key && x.Value.TableName == block.TableName);
        if (oldBlock is { Value: not null})
            AddBuilderForm.Blocks.Remove(oldBlock.Key);

        block.FilterDefinition = definitions[block.TableName];
        block.FilterInputs.Clear();
        block.FilterInputs = new() { { SecurityDriven.FastGuid.NewGuid(), new ComplexFilter() } };
        StateHasChanged();
    }

    private void AddBlock(MessagingGroupBuilderVM builder)
    {
        var availableTableNames = definitions.Keys.Where(y => !builder.Blocks.Values.Any(x => x.TableName == y)).ToList();

        builder.Blocks[SecurityDriven.FastGuid.NewGuid()] = new()
        {
            FilterDefinition = definitions[availableTableNames[0]],
                FilterInputs = new() { { SecurityDriven.FastGuid.NewGuid(), new ComplexFilter() } },
            TableName = availableTableNames[0],
            TableNames = availableTableNames,
            Condition = FilterCondition.Or,
            ShouldHaveCondition = true,
        };

        if (availableTableNames.Count == 1)
        {
            ShowAddDataSource = false;
            StateHasChanged();
        }
    }

    private void DeleteBlock(Guid id, MessagingGroupBuilderVM builder)
    {
        ShowAddDataSource = true;
        builder.Blocks.Remove(id);
        StateHasChanged();
    }

    private void ClearAllBlocks(MessagingGroupBuilderVM builder)
    {
        ShowAddDataSource = true;
        builder.Blocks.Clear();
        builder.Blocks[SecurityDriven.FastGuid.NewGuid()] = new()
            {
                FilterDefinition = definitions[builder.TableNames[0]],
                FilterInputs = new() { { Guid.NewGuid(), new ComplexFilter() } },
                TableName = builder.TableNames[0],
                TableNames = builder.TableNames
            };
    }

    private void AddFilter(MessageGroupBuilderBlock block)
    {
        block.FilterInputs.Add(SecurityDriven.FastGuid.NewGuid(), new ComplexFilter { ShouldHaveCondition = true });
        StateHasChanged();
    }

    private void ClearFilter(MessageGroupBuilderBlock block)
    {
        block.FilterInputs.Clear();
        block.FilterInputs = new() { { SecurityDriven.FastGuid.NewGuid(), new ComplexFilter() } };
    }

    private void DeleteFilter(MessageGroupBuilderBlock block, Guid id)
    {
        block.FilterInputs.Remove(id);
        StateHasChanged();
    }

    private void GenerateQuery()
    {
        ModalMessage.Close();
        var result = groupBuilder.GenerateQuery<CustomerProfile, MessageGroupUserForBuilder>(AddBuilderForm, x => new() { FullName = x.FullName, EmailAddress = x.Email, AccountId = x.AccountId, PhoneNumber = x.PhoneNumber.Number, MobileNumber = x.MobileNumber.Number});
        if (result.IsSuccessful)
            AddBuilderForm.Query = result.Data;
        else
        {
            ModalMessage.Error(result.Message);
        }
    }

    private async Task PreviewBuilderData()
    {
        ModalMessage.Close();
        GroupModalMessage.Close();


        await groupPreviewTable.LoadElement();

        StateHasChanged();
    }

    private async ValueTask<TypedBasePageList<MessageGroupUserForBuilder>> LoadBuilderPreview(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        try
        {
            var result = await groupBuilder.GetPreviewData(AddBuilderForm.Query, filterAndPage, ct);

            if(AddBuilderForm.WriteOwnSqlQuery)
            {
                AddBuilderForm.CustomQueryValidated = true;
                AddBuilderForm.CustomQuery = AddBuilderForm.Query;
            }
            await JSRuntime.OpenModal(PreviewBuilderModalName, Cancel);

            return result;
        }
        catch (Exception)
        {
            ModalMessage.Error("Could not generate data for preview. Please check query is correct");
            return new TypedBasePageList<MessageGroupUserForBuilder>();
        }
    }


    protected async ValueTask SubmitBuilder()
    {
        ModalMessage.Close();
        GroupModalMessage.Close();

        var data = AddBuilderForm.Get(Selected.Id);

        if(!AddBuilderForm.WriteOwnSqlQuery)
        {
            var result = groupBuilder.GenerateQuery<CustomerProfile, object>(AddBuilderForm, x => x.Id);
            if (result.IsFailed)
            {
                ModalMessage.Error(result.Message);
                return;
            }
            data.SenderQuery = result.Data;
        }
        else
        {
            if (string.IsNullOrEmpty(AddBuilderForm.CustomQuery) || !AddBuilderForm.CustomQueryValidated || !AddBuilderForm.CustomQuery.Contains("id", StringComparison.OrdinalIgnoreCase))
            {
                ModalMessage.Error(string.IsNullOrEmpty(AddBuilderForm.CustomQuery) ? "Please enter a valid query" : "Preview first to validate query before saving");
                return;
            }

            data.SenderQuery = AddBuilderForm.CustomQuery;
        }

        

        if(data.Id > 0)
            groupService.CrudService.Db.Update(data);
        else
            groupService.CrudService.Db.Insert(data);

        await groupService.CrudService.Uow.SaveAsync(Cancel);
        
        TableMessage.Success("Data saved successfully");
        await JSRuntime.CloseModal(AddBuilderModalName, Cancel);

        StateHasChanged();
    }
    #endregion
}
