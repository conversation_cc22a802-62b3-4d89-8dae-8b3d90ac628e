﻿using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public sealed class CustomerEarningsArrestmentVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<CustomerEarningsArrestment, CustomerEarningsArrestmentVM>> Mapping = data => new()
    {
        Id = data.Id,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId,
        SiliconReference = data.SiliconReference,
        ScannedDate = data.ScannedDate,
        LatestUpdateDescription = data.LatestUpdateDescription,
        LatestUpdateDate = data.LatestUpdateDate,
        LatestUpdate = data.LatestUpdate,
        ActionRequired = data.ActionRequired,
        Court = data.Court,
        CaseReference = data.CaseReference,
        AEReference = data.AEReference
    };

    public CustomerEarningsArrestment Get(string profileId) => new()
    {
        Id = Id,
        ProfileId = profileId,
        ActionRequired = ActionRequired,
        AEReference = AEReference,
        CaseReference = CaseReference,
        Court = Court,
        LatestUpdate = LatestUpdate,
        LatestUpdateDate = LatestUpdateDate,
        LatestUpdateDescription = LatestUpdateDescription,
        ScannedDate = ScannedDate,
        SiliconReference = SiliconReference,

    };

    public string SiliconReference { get; set; }
    public string CaseReference { get; set; }
    public string AEReference { get; set; }
    public string Court { get; set; }
    public string LatestUpdate { get; set; }
    public DateOnly? LatestUpdateDate { get; set; }
    public string LatestUpdateDescription { get; set; }
    public string ActionRequired { get; set; }
    public DateOnly? ScannedDate { get; set; }
}

public enum CustomerEarningsArrestmentActionRequired
{
    Collection,
    Ignore,
    Legal,
}