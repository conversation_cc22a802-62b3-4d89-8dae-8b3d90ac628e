﻿using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Specification;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;
public partial class ViewProfile
{
    private DataTable<CustomerLegalVM> customerLegalsTable;
    private ColumnList customerLegalTableDefinition;
    private string AddCustomerLegalModal => "AddCustomerLegalModal";
    private string EditCustomerLegalModal => "EditCustomerLegalModal";

    private CustomerLegal AddCustomerLegalModel { get; set; } = new();
    private CustomerLegal EditCustomerLegalModel { get; set; } = new();

    private void SetupCustomerLegal()
    {
        customerLegalTableDefinition = CrudService.GetTableDefinition<CustomerLegal, CustomerLegalVM>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerLegalPermission),
            HasEdit = HasClaim(ManageCustomersNavigation.CustomerProfileViewEditCustomerLegalPermission),
        });

        customerLegalTableDefinition.TopActionButtons.Add(new TopActionButton("Add", ModalName: AddCustomerLegalModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddCustomerLegalPermission)));

        customerLegalsTable.SetTableDefinition(customerLegalTableDefinition);

    }

    private ValueTask<TypedBasePageList<CustomerLegalVM>> LoadCustomerLegal(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerLegal>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, CustomerLegalVM.Mapping, ct);
    }

    private ValueTask SubmitNewCustomerLegal() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddCustomerLegalPermission, AddCustomerLegalModal, async () =>
    {
        AddCustomerLegalModel.ProfileId = Data.Id;
        uow.Db.Insert(AddCustomerLegalModel);
        await uow.SaveAsync(Cancel);

        return true;
    }, () =>
    {
        AddCustomerLegalModel = new();
        StateHasChanged();
        return customerLegalsTable.Refresh();
    });

    private ValueTask StartEditCustomerLegal(CustomerLegalVM data, CancellationToken ct) => BaseEdit(ManageCustomersNavigation.CustomerProfileViewEditCustomerLegalPermission, EditCustomerLegalModal, () =>
    {
        EditCustomerLegalModel = data.Get(Data.Id);
        return Task.CompletedTask;
    }, ct);

    private ValueTask SubmitEditCustomerLegal() => BaseSaveEdit(null, EditCustomerLegalModal, async () =>
    {
        uow.Db.Update(EditCustomerLegalModel);
        await uow.SaveAsync(Cancel);
        return true;
    }, customerLegalsTable.Refresh);

    private ValueTask<bool> DeleteCustomerLegal(CustomerLegalVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeleteCustomerLegalPermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerLegal>(x => x.Id == data.Id, ct);
        return result > 0;
    }, refresh);

}
