﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Infrastructure.Core.ViewModels.Messaging;

public sealed class CreateCustomMessageVM
{
    [ValidString(ValidStringRule.NoScriptTag)]
    public string Subject { get; set; }
    [ValidString(ValidStringRule.NoScriptTag)]
    public string Text { get; set; }
    [ValidString(ValidStringRule.NoScriptTag)]
    public string Html { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public MessageChannel Channels { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string ChannelsFormatted => string.Join(", ", Channels.FlagsToDisplayList<MessageChannel>());
    [RemoveColumn, Required]
    public List<MessageChannel> ChannelsList { get; set; } = [];
    public MessagingTemplate ContainerTemplate { get; set; }
    public long? ContainerTemplateId { get; set; }
    public string EmailAddress { get; set; }
    public PhoneNumber PreferredPhoneNumber { get; set; }
    [DataType(DataType.EmailAddress)]
    [ValidString(ValidStringRule.NoScriptTag)]
    public string SenderEmail { get; set; }
}

public sealed class CustomMessageContactVM
{
    public string EmailAddress { get; set; }
    public PhoneNumber PhoneNumber { get; set; }
    public string DisplayType { get; set; }
    public string DisplayText
    {
        get
        {
            if (!string.IsNullOrEmpty(EmailAddress))
            {
                return $"{DisplayType}: {EmailAddress}";
            }
            else if (PhoneNumber != null && PhoneNumber.IsValid())
            {
                return $"{DisplayType}: {PhoneNumber.Code} {PhoneNumber.Number}";
            }
            return string.Empty;
        }
    }
}

public sealed class CustomMessageVM
{
    public string Subject { get; set; }
    public string Body { get; set; }
    public string SenderEmail { get; set; }
    public MessageChannel Channels { get; set; }
    public MessagingTemplate ContainerTemplate { get; set; }
}

