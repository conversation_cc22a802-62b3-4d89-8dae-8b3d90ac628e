﻿namespace LendQube.Infrastructure.Core.Database.Repository.Concurrency;

internal abstract class AbstractQueuedDbRequest
{
    public CancellationToken CancellationToken { get; }
    public CancellationTokenSource CancellationTokenSource { get; }

    public AbstractQueuedDbRequest(CancellationToken cancellationToken)
    {
        CancellationToken = cancellationToken;
    }

    public AbstractQueuedDbRequest(CancellationTokenSource cancellationTokenSource, CancellationToken cancellationToken)
    {
        CancellationToken = cancellationToken;
        CancellationTokenSource = cancellationTokenSource;
    }

    public abstract Task Execute();
    public abstract void SetException(Exception exception);
    public abstract void SetCanceled();
}


internal class QueuedDbRequest<T>(Func<Task<T>> operation, TaskCompletionSource<T> tcs, CancellationToken cancellationToken) :
    AbstractQueuedDbRequest(cancellationToken)
{
    public override async Task Execute()
    {
        var result = await operation();
        tcs.SetResult(result);
    }

    public override void SetException(Exception exception)
    {
        tcs.SetException(exception);
    }

    public override void SetCanceled()
    {
        tcs.SetCanceled();
    }
}

internal class QueuedDbRequest(Func<Task> operation, TaskCompletionSource<object> tcs, CancellationToken cancellationToken) :
    AbstractQueuedDbRequest(cancellationToken)
{
    public override async Task Execute()
    {
        await operation();
        tcs.SetResult(null);
    }

    public override void SetException(Exception exception)
    {
        tcs.SetException(exception);
    }

    public override void SetCanceled()
    {
        tcs.SetCanceled();
    }
}


internal class QueuedValueTaskDbRequest<T> :
    AbstractQueuedDbRequest
{
    private readonly Func<ValueTask<T>> operation;
    private readonly TaskCompletionSource<T> tcs;

    public QueuedValueTaskDbRequest(Func<ValueTask<T>> operation, TaskCompletionSource<T> tcs, CancellationToken cancellationToken) : base(cancellationToken)
    {
        this.operation = operation;
        this.tcs = tcs;
    }

    public QueuedValueTaskDbRequest(Func<ValueTask<T>> operation, TaskCompletionSource<T> tcs, CancellationTokenSource cancellationTokenSource, CancellationToken cancellationToken) : base(cancellationTokenSource, cancellationToken)
    {
        this.operation = operation;
        this.tcs = tcs;
    }

    public override async Task Execute()
    {
        try
        {
            var result = await operation();
            tcs.SetResult(result);
        }
        catch (OperationCanceledException)
        {
            tcs.SetResult(default);
        }
    }

    public override void SetException(Exception exception)
    {
        tcs.SetException(exception);
    }

    public override void SetCanceled()
    {
        tcs.SetCanceled();
    }
}