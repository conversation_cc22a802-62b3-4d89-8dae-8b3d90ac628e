﻿using System.Collections.Concurrent;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Messaging;

internal sealed class MessageComposer
{
    public string OriginatedFrom { get; set; }
    public ConcurrentDictionary<string, MessageBrick> Messages { get; set; } = [];
    public ConcurrentBag<CustomMessageBrick> CustomMessages { get; set; } = [];
    public string CreatedByUserId { get; set; }

    public async Task<Result<long>> BuildAndQueue<T>(IUnitofWork uow, ILogManager<T> logger, CancellationToken ct) where T : class //safe method
    {
        try
        {
            return await BuildAndQueue(uow, ct);
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Messaging, EventAction.Processing, ex, "Message could not be built", data: this);
        }
        return "Building message failed";
    }

    public async ValueTask<Result<long>> BuildAndQueue(IUnitofWork uow, CancellationToken ct)
    {
        ArgumentNullException.ThrowIfNull(OriginatedFrom, nameof(OriginatedFrom));

        Messages.AsParallel().Where(x =>
            (!x.Value.ConfigId.HasValue && string.IsNullOrEmpty(x.Value.ConfigName)) ||
            x.Value.Recipients.IsNullOrEmpty())
            .ForAll((item) => Messages.TryRemove(item));

        var validCustomMessages = CustomMessages.Where(x => !x.Recipients.IsNullOrEmpty()).ToList();

        if (Messages.Values.IsNullOrEmpty() && validCustomMessages.IsNullOrEmpty())
            return "No recipients";

        if (Messages.AsParallel().Any(x =>
            x.Value.Recipients.IsNullOrEmpty() ||
            (!x.Value.ConfigId.HasValue && string.IsNullOrEmpty(x.Value.ConfigName))))
            throw new InvalidOperationException($"Configured {nameof(Messages)} not properly set up - missing configuration or recipients");

        if (Messages.AsParallel().Any(x =>
            x.Value.Recipients.Any(y => !y.IsValid() && string.IsNullOrEmpty(x.Value.GroupName))))
            throw new InvalidOperationException("Configured message recipient info not properly constructed - invalid recipients without group name");

        if (validCustomMessages.AsParallel().Any(x => x.Recipients.IsNullOrEmpty()))
            throw new InvalidOperationException("Custom messages must have recipients");

        if (validCustomMessages.AsParallel().Any(x => x.Recipients.Any(y => !y.IsValid())))
            throw new InvalidOperationException("Custom message recipients are not valid - missing required recipient information");

        var configIds = Messages.AsParallel().Where(x => x.Value.ConfigId.HasValue).Select(x => x.Value.ConfigId.Value).ToList();
        if (!configIds.IsNullOrEmpty() && !await MessagingCompiledQueries.DoesMessageConfigsExist(uow, configIds, ct))
            throw new InvalidOperationException("Some provided config ids do not exist");

        var message = new MessageLog { OriginatedFrom = OriginatedFrom, CreatedByUserId = CreatedByUserId };
        var messageConfigs = await PrepareMessageConfigs(uow, message, ct);

        uow.Db.Insert(message);

        var hasEntries = await PrepareRecipients(uow, message, messageConfigs, validCustomMessages, ct);

        var successful = false;
        if (!hasEntries)
        {
            //rollback, no message to send;
            uow.Db.RollbackSingle(message);
        }
        else
        {
            uow.Db.Insert(new MessageLogActivity
            {
                MessageLogId = message.Id,
                Title = message.Status.ToString(),
                Activity =
                message.Status == MessageStatus.WaitingForConfiguration ? "Configuration required" : "Queued for processing"
            });
            successful = true;
        }

        await uow.SaveAsync(ct);
        return successful ? message.Id : "Building message failed";
    }

    private ValueTask<List<ConfigIdsAndUsersToSkip>> CheckDoNotSendIfExists(IUnitofWork uow, CancellationToken ct)
    {
        var parallelMessages = Messages.AsParallel();
        var configIds = parallelMessages.Where(x => x.Value.ConfigId.HasValue).Select(x => x.Value.ConfigId).Distinct();
        var userIds = parallelMessages.SelectMany(x => x.Value.Recipients.Where(y => !string.IsNullOrEmpty(y.UserId)).Select(y => y.UserId)).Distinct();
        var contactGroupIds = parallelMessages.SelectMany(x => x.Value.Recipients.Where(y => y.MessagingGroupId.HasValue).Select(y => y.MessagingGroupId)).Distinct();

        return MessagingCompiledQueries.GetMessageExistsForRecipientData(uow, configIds, userIds, contactGroupIds, ct);
    }

    private async ValueTask<List<MessageConfigIdAndName>> PrepareMessageConfigs(IUnitofWork uow, MessageLog message, CancellationToken ct)
    {
        var configNames = Messages.AsParallel().Where(x => !string.IsNullOrEmpty(x.Value.ConfigName) && !x.Value.ConfigId.HasValue).Select(x => x.Value.ConfigName);

        var existingConfigData = await MessagingCompiledQueries.GetMessageConfigIdsAndNamesByNames(uow, configNames, ct);
        var unconfiguredMessageConfigs = configNames.Where(x => !existingConfigData.Any(y => y.Name.Equals(x, StringComparison.OrdinalIgnoreCase)));

        ConcurrentBag<MessageConfiguration> newMessageConfigs = [];
        var unconfiguredCount = unconfiguredMessageConfigs.Count();
        if (unconfiguredCount > 0)
        {
            Parallel.ForEach(unconfiguredMessageConfigs, new ParallelOptions { MaxDegreeOfParallelism = unconfiguredCount, CancellationToken = ct }, (config) =>
            {
                newMessageConfigs.Add(new MessageConfiguration
                {
                    Name = config,
                    Subject = config,
                    Channels = MessageChannel.Email,
                    Description = $"{OriginatedFrom}: {config}",
                    Keys = Messages[config].Recipients[0].TemplateValues?.AsParallel().Select(x => x.Key).ToList(),
                });
            });
            await uow.Db.InsertBulkAsync(newMessageConfigs, ct);
        }

        if (!newMessageConfigs.IsEmpty || existingConfigData.Any(x => x.NeedsConfiguration))
        {
            message.Status = MessageStatus.WaitingForConfiguration;
            existingConfigData.AddRange(newMessageConfigs.Select(x => new MessageConfigIdAndName { Id = x.Id, Name = x.Name }));
        }
        else
        {
            message.Status = MessageStatus.Queued;
        }

        return existingConfigData;
    }

    private async Task<bool> PrepareRecipients(IUnitofWork uow, MessageLog message, List<MessageConfigIdAndName> configs, List<CustomMessageBrick> customMessages, CancellationToken ct)
    {
        var messageEntries = new ConcurrentBag<MessageLogEntry>();

        // Process configured messages 
        if (!Messages.IsEmpty)
        {
            await ProcessConfiguredMessages(uow, message, configs, messageEntries, ct);
        }

        // Process custom messages
        if (customMessages.Count > 0)
        {
            ProcessCustomMessages(message, customMessages, messageEntries, ct);
        }

        uow.Db.InsertBulk(messageEntries, ct);

        var result = !messageEntries.IsEmpty;
        messageEntries.Clear();
        return result;
    }

    private async Task ProcessConfiguredMessages(IUnitofWork uow, MessageLog message, List<MessageConfigIdAndName> configs, ConcurrentBag<MessageLogEntry> messageEntries, CancellationToken ct)
    {
        //set group ids for named groups
        if (Messages.Values.Any(x => !string.IsNullOrEmpty(x.GroupName)))
        {
            var groupNames = Messages.Values.Select(x => x.GroupName);
            var namedGroupIds = await uow.Db.ManySelectAsync(Query<MessagingGroup, NamedGroupsNamesAndIds>.Where(x => groupNames.Contains(x.Name)).Select(x => new NamedGroupsNamesAndIds { Id = x.Id, Name = x.Name }), ct);
            Messages.Values.Where(x => !string.IsNullOrEmpty(x.GroupName))
                .AsParallel().ForAll(x =>
                {
                    x.Recipients.Where(y => !y.IsValid())
                    .AsParallel().ForAll(y =>
                    {
                        y.MessagingGroupId = namedGroupIds.FirstOrDefault(z => z.Name == x.GroupName)?.Id;
                    });
                });
        }

        var doNotSend = await CheckDoNotSendIfExists(uow, ct);
        var doNotSendIfExists = doNotSend.AsParallel();

        var configIds = Messages.Select(item => (item.Value.ConfigId ?? configs.FirstOrDefault(x => x.Name.Equals(item.Value.ConfigName, StringComparison.OrdinalIgnoreCase))?.Id));

        var allConfigs = await uow.Db.ManyAsync(Query<MessageConfiguration>.Where(x => configIds.Contains(x.Id))
            .Include(x => x.Include(y => y.ContainerTemplate).Include(y => y.BodyTemplate))
            , ct);

        Parallel.ForEach(Messages, new ParallelOptions { MaxDegreeOfParallelism = Messages.Count, CancellationToken = ct }, (item) =>
        {
            var configId = item.Value.ConfigId ?? configs.FirstOrDefault(x => x.Name.Equals(item.Value.ConfigName, StringComparison.OrdinalIgnoreCase))?.Id;

            if (!configId.HasValue)
                return;

            var recipients = item.Value.Recipients.AsParallel()
                .Where(x =>
                !doNotSendIfExists.Any(y => y.ConfigId == configId && (y.UserId == x.UserId || y.MessagingGroupId == x.MessagingGroupId))
                && x.IsValid())
                .Select(x => new MessageRecipient { UserId = x.UserId, MessagingGroupId = x.MessagingGroupId, AdHoc = x.AdHoc, Attachments = x.Attachments, TemplateValues = x.TemplateValues });

            if (!recipients.IsNullOrEmpty())
            {
                var config = allConfigs.FirstOrDefault(x => x.Id == configId);
                if (config != null)
                {
                    messageEntries.Add(new MessageLogEntry
                    {
                        MessageLogId = message.Id,
                        MessageConfigurationId = configId.Value,
                        Recipients = [.. recipients],
                        Name = config.Name,
                        Subject = config.Subject,
                        Channels = config.Channels,
                        Keys = config.Keys,
                        TextRequired = !config.NeedsConfiguration && config.TextRequired,
                        EmailRequired = !config.NeedsConfiguration && config.EmailRequired,
                        TextTemplate = config.TextTemplate,
                        HtmlTemplate = config.HtmlTemplate,
                        ContainerTextTemplate = config.ContainerTemplate?.TextTemplate,
                        ContainerHtmlTemplate = config.ContainerTemplate?.HtmlTemplate,
                        BodyTextTemplate = config.BodyTemplate?.TextTemplate,
                        BodyHtmlTemplate = config.BodyTemplate?.HtmlTemplate,
                    });
                }
                else
                {
                    messageEntries.Add(new MessageLogEntry
                    {
                        MessageLogId = message.Id,
                        MessageConfigurationId = configId.Value,
                        Recipients = [.. recipients]
                    });
                }
            }
        });

        // Handle do not send logic (only for configured messages)
        if (!doNotSendIfExists.IsNullOrEmpty())
        {
            var doNotSendIds = doNotSendIfExists.Select(x => x.MessageLogId).Distinct();
            await uow.Db.UpdateAndSaveWithFilterAsync<MessageLog>(x => doNotSendIds.Contains(x.Id), x => x.SetProperty(y => y.AttemptCount, y => y.AttemptCount + 1), ct);

            var activities = doNotSendIfExists.DistinctBy(x => x.MessageLogId).Select(x => new MessageLogActivity
            {
                MessageLogId = x.MessageLogId,
                Title = "Do not send if exists",
                Activity = $"Triggered for recipients {(!string.IsNullOrEmpty(x.UserId)
                ? "User Ids: " + string.Join(", ", doNotSendIfExists.Where(y => y.MessageLogId == x.MessageLogId).Select(y => y.UserId).Distinct())
                : "")}  {(x.MessagingGroupId.HasValue
                ? "Messaging Group Ids: " + string.Join(", ", doNotSendIfExists.Where(y => y.MessageLogId == x.MessageLogId).Select(y => y.MessagingGroupId).Distinct()) : ""
                )}"
            }).ToList();

            uow.Db.InsertBulk(activities, ct);
        }
    }

    private void ProcessCustomMessages(MessageLog message, List<CustomMessageBrick> customMessages, ConcurrentBag<MessageLogEntry> messageEntries, CancellationToken ct)
    {
        Parallel.ForEach(customMessages, new ParallelOptions { MaxDegreeOfParallelism = customMessages.Count, CancellationToken = ct }, (item) =>
        {
            var recipients = item.Recipients.AsParallel().Where(x => x.IsValid());

            if (!recipients.IsNullOrEmpty())
            {
                var isEmail = item.Channels.HasFlag(MessageChannel.Email);
                var isSms = item.Channels.HasFlag(MessageChannel.Sms);
                var isPush = item.Channels.HasFlag(MessageChannel.PushNotification);

                var entry = new MessageLogEntry
                {
                    MessageLogId = message.Id,
                    Recipients = [.. recipients],
                    Name = "SendCustomMessage",
                    Subject = item.Subject,
                    SenderEmail = item.SenderEmail,
                    Channels = item.Channels,
                    Keys = recipients.FirstOrDefault()?.TemplateValues?.Select(x => x.Key).ToList() ?? [],
                    TextRequired = isSms || isPush,
                    EmailRequired = isEmail,
                    ContainerTextTemplate = isSms || isPush ? item.TextContainerTemplate : null,
                    ContainerHtmlTemplate = isEmail ? item.HtmlContainerTemplate : null,
                };
                messageEntries.Add(entry);
            }
        });
    }
}

public sealed class MessageBrick
{
    internal MessageComposer Composer { get; set; }
    internal long? ConfigId { get; set; }
    internal string ConfigName { get; set; }
    internal string GroupName { get; set; }
    internal List<MessageRecipient> Recipients { get; set; } = [];
}

public sealed class CustomMessageBrick
{
    internal MessageComposer Composer { get; set; }
    internal List<MessageRecipient> Recipients { get; set; } = [];
    internal MessageChannel Channels { get; set; }
    internal string Subject { get; set; }
    internal string SenderEmail { get; set; }
    internal string TextContainerTemplate { get; set; }
    internal string HtmlContainerTemplate { get; set; }
}

internal sealed class ConfigIdsAndUsersToSkip
{
    public long MessageLogId { get; set; }
    public long ConfigId { get; set; }
    public string UserId { get; set; }
    public long? MessagingGroupId { get; set; }
}

internal sealed class NamedGroupsNamesAndIds
{
    public long Id { get; set; }
    public string Name { get; set; }
}