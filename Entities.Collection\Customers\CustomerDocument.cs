﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LendQube.Entities.Collection.Customers;
public class CustomerDocument : BaseEntityWithIdentityId<CustomerDocument>
{
    [DbGuid]
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.NoScriptTag)]
    public string Name { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string FileUrl { get; set; }

}
