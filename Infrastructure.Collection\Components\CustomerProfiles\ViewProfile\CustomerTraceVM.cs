﻿using System.ComponentModel;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public sealed class CustomerTraceVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<CustomerTrace, CustomerTraceVM>> Mapping = data => new()
    {
        Id = data.Id,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId,
        RetrieverTraceResult = data.RetrieverTraceResult,
        RetrieverTraceDone = data.RetrieverTraceDone,
        RetrieverTraceDate = data.RetrieverTraceDate,
        ProceedWithLegal = data.ProceedWithLegal,
        OtherTraceResult = data.OtherTraceResult,
        OtherTraceDone = data.OtherTraceDone,
        OtherTraceCompanyName = data.OtherTraceCompanyName,
        OtherTraceCompanyDate = data.OtherTraceCompanyDate,
        HomeOwner = data.HomeOwner,
        CCJValue = data.CCJValue,
        CCJVolume = data.CCJVolume,
        DODTraceDate = data.DODTraceDate,
        DODTraceDone = data.DODTraceDone,
        DODTraceResult = data.DODTraceResult,
        EndeavourTraceDate = data.EndeavourTraceDate,
        EndeavourTraceDone = data.EndeavourTraceDone,
        EndeavourTraceResult = data.EndeavourTraceResult
    };

    public CustomerTrace Get(string profileId) => new()
    {
        Id = Id,
        ProfileId = profileId,
        CCJValue = CCJValue,
        CCJVolume = CCJVolume,
        DODTraceDate = DODTraceDate,
        DODTraceDone = DODTraceDone,
        DODTraceResult = DODTraceResult,
        EndeavourTraceDate = EndeavourTraceDate,
        EndeavourTraceDone = EndeavourTraceDone,
        EndeavourTraceResult = EndeavourTraceResult,
        HomeOwner = HomeOwner,
        OtherTraceCompanyDate = OtherTraceCompanyDate,
        OtherTraceCompanyName = OtherTraceCompanyName,
        OtherTraceDone = OtherTraceDone,
        OtherTraceResult = OtherTraceResult,
        ProceedWithLegal = ProceedWithLegal,
        RetrieverTraceDate = RetrieverTraceDate,
        RetrieverTraceDone = RetrieverTraceDone,
        RetrieverTraceResult = RetrieverTraceResult
    };

    public string RetrieverTraceDone { get; set; }
    public DateOnly? RetrieverTraceDate { get; set; }
    public string RetrieverTraceResult { get; set; }
    public string CCJVolume { get; set; }
    public string CCJValue { get; set; }
    public string HomeOwner { get; set; }
    public string ProceedWithLegal { get; set; }
    public string DODTraceDone { get; set; }
    public DateOnly? DODTraceDate { get; set; }
    public string DODTraceResult { get; set; }
    public string EndeavourTraceDone { get; set; }
    public DateOnly? EndeavourTraceDate { get; set; }
    public string EndeavourTraceResult { get; set; }
    public string OtherTraceDone { get; set; }
    public string OtherTraceCompanyName { get; set; }
    public DateOnly? OtherTraceCompanyDate { get; set; }
    public string OtherTraceResult { get; set; }
}

public enum CustomerTraceRetrieverTraceDone
{
    Yes,
    No
}

public enum CustomerTraceRetrieverTraceResult
{
    [Description("Living as Stated")]
    LivingAsStated,
    [Description("Living as Stated 2nd Address")]
    LivingAsStated2ndAddress,
    [Description("New Address")]
    NewAddress,
    [Description("Unable to Confirm")]
    UnableToConfirm
}

public enum CustomerTraceHomeOwner
{
    Yes,
    No
}

public enum CustomerTraceProceedWithLegal
{
    Yes,
    No,
    Review
}

public enum CustomerTraceDODTraceDone
{
    Yes,
    No
}

public enum CustomerTraceDODTraceResult
{
    [Description("Living as Stated")]
    LivingAsStated,
    [Description("New Address")]
    NewAddress,
    [Description("Unable to Confirm")]
    UnableToConfirm
}

public enum CustomerTraceEndeavourTraceDone
{
    Yes,
    No
}

public enum CustomerTraceEndeavourTraceResult
{
    Benefits,
    EmployedPAYE,
    SelfEmployed,
    Student,
    UnableToConfirm,
}

public enum CustomerTraceOtherTraceDone
{
    Yes,
    No
}
