﻿using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public sealed class CustomerFeeVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<CustomerFee, CustomerFeeVM>> Mapping = data => new()
    {
        Id = data.Id,
        CourtFee = data.CourtFee,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId,
        TraceInformDate = data.TraceInformDate,
        TraceInform = data.TraceInform,
        TraceEndGTCDate = data.TraceEndGTCDate,
        TraceEndGTC = data.TraceEndGTC,
        CourtFeeDate = data.CourtFeeDate,
        CostOfRaisingClaimDate = data.CostOfRaisingClaimDate,
        CostOfRaisingClaim = data.CostOfRaisingClaim,
        CAPSFeeDate = data.CAPSFeeDate,
        CAPSFee = data.CAPSFee
    };

    public CustomerFee Get(string profileId) => new()
    {
        Id = Id,
        ProfileId = profileId,
        CourtFee = CourtFee,
        CAPSFee = CAPSFee,
        CAPSFeeDate = CAPSFeeDate,
        CostOfRaisingClaim = CostOfRaisingClaim,
        CostOfRaisingClaimDate = CostOfRaisingClaimDate,
        CourtFeeDate = CourtFeeDate,
        TraceEndGTC = TraceEndGTC,
        TraceEndGTCDate = TraceEndGTCDate,
        TraceInform = TraceInform,
        TraceInformDate = TraceInformDate,
    };

    public decimal TraceEndGTC { get; set; }
    public DateOnly? TraceEndGTCDate { get; set; }
    public decimal TraceInform { get; set; }
    public DateOnly? TraceInformDate { get; set; }
    public decimal CourtFee { get; set; }
    public DateOnly? CourtFeeDate { get; set; }
    public decimal CostOfRaisingClaim { get; set; }
    public DateOnly? CostOfRaisingClaimDate { get; set; }
    public decimal CAPSFee { get; set; }
    public DateOnly? CAPSFeeDate { get; set; }
}

